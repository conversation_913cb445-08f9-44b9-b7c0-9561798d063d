"""
Script utilitaire pour exécuter les tests Salesforce.

Ce script facilite l'exécution des tests d'intégration Salesforce
avec gestion des credentials et configuration.
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path
from typing import Dict, Any
from loguru import logger
from simple_salesforce import Salesforce
from simple_salesforce.exceptions import SalesforceAuthenticationFailed

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from tests.salesforce_test_data.salesforce_test_manager import SalesforceTestManager
from tests.salesforce_test_data.test_data_generator import TestDataGenerator
from src.core.models import DataSource, SourceType
from src.extractors.salesforce_extractor import SalesforceExtractor
from src.core.salesforce_auth import create_authenticator_from_env


class SalesforceTestRunner:
    """
    Runner pour les tests Salesforce avec gestion complète du cycle de vie.
    """
    
    def __init__(self):
        self.sf_connection = None
        self.test_manager = None
        self.data_generator = TestDataGenerator()
        self.extractor = None
    
    def setup_logging(self, log_level: str = "INFO"):
        """Configure le logging."""
        logger.remove()
        logger.add(
            sys.stdout,
            level=log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
        logger.add(
            "salesforce_tests.log",
            level="DEBUG",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB"
        )
    
    def get_salesforce_config(self) -> Dict[str, Any]:
        """
        Récupère la configuration Salesforce depuis les variables d'environnement.

        Variables d'environnement attendues :
        - SF_USERNAME : Nom d'utilisateur Salesforce
        - SF_PASSWORD : Mot de passe Salesforce
        - SF_SECURITY_TOKEN : Token de sécurité Salesforce (optionnel)
        - SF_CONSUMER_KEY : Clé consumer OAuth (optionnel)
        - SF_CONSUMER_SECRET : Secret consumer OAuth (optionnel)
        - SF_AUTH_TYPE : Type d'authentification (password ou oauth, défaut: password)
        - SF_DOMAIN : Domaine Salesforce (login ou test pour sandbox)
        - SF_API_VERSION : Version de l'API (optionnel, défaut: 58.0)
        """
        config = {
            'username': os.getenv('SF_USERNAME'),
            'password': os.getenv('SF_PASSWORD'),
            'security_token': os.getenv('SF_SECURITY_TOKEN', ''),
            'consumer_key': os.getenv('SF_CONSUMER_KEY'),
            'consumer_secret': os.getenv('SF_CONSUMER_SECRET'),
            'auth_type': os.getenv('SF_AUTH_TYPE', 'password').lower(),
            'domain': os.getenv('SF_DOMAIN', 'login'),
            'api_version': os.getenv('SF_API_VERSION', '58.0')
        }

        # Vérifier que les paramètres obligatoires sont présents
        required_params = ['username', 'password']
        missing_params = [param for param in required_params if not config[param]]

        if missing_params:
            raise ValueError(f"Paramètres Salesforce manquants: {', '.join(missing_params)}")

        # Vérifier les paramètres OAuth si nécessaire
        if config['auth_type'] == 'oauth':
            oauth_params = ['consumer_key', 'consumer_secret']
            missing_oauth = [param for param in oauth_params if not config[param]]
            if missing_oauth:
                raise ValueError(f"Paramètres OAuth manquants: {', '.join(missing_oauth)}")

        return config
    
    async def connect_to_salesforce(self) -> bool:
        """Établit la connexion à Salesforce."""
        try:
            config = self.get_salesforce_config()
            logger.info(f"Connexion à Salesforce ({config['auth_type']}): {config['username']} sur {config['domain']}")

            # Utiliser le nouveau système d'authentification
            authenticator = create_authenticator_from_env()
            self.sf_connection = authenticator.authenticate()

            # Test de la connexion
            user_info = self.sf_connection.query("SELECT Id, Name, Username FROM User LIMIT 1")

            if user_info['records']:
                logger.info(f"Connexion réussie - Utilisateur: {user_info['records'][0]['Name']}")
            else:
                logger.info("Connexion réussie - Informations utilisateur non disponibles")

            # Afficher les informations de connexion
            conn_info = authenticator.get_connection_info()
            logger.info(f"Type d'authentification: {conn_info['auth_type']}")
            logger.info(f"Domaine: {conn_info['domain']}")
            logger.info(f"Version API: {conn_info['api_version']}")

            return True

        except SalesforceAuthenticationFailed as e:
            logger.error(f"Échec de l'authentification Salesforce: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Erreur de connexion Salesforce: {str(e)}")
            return False
    
    async def setup_test_environment(self) -> bool:
        """Configure l'environnement de test."""
        if not self.sf_connection:
            logger.error("Connexion Salesforce requise")
            return False
        
        self.test_manager = SalesforceTestManager(self.sf_connection)
        
        logger.info("Configuration de l'environnement de test Salesforce")
        return await self.test_manager.setup_test_environment()
    
    async def run_schema_discovery_test(self) -> bool:
        """Teste la découverte de schéma."""
        try:
            logger.info("Test de découverte de schéma")
            
            # Créer la configuration de source de données
            config = self.get_salesforce_config()
            data_source = DataSource(
                name="Test Salesforce Org",
                source_type=SourceType.SALESFORCE,
                connection_config=config
            )
            
            # Créer l'extracteur
            self.extractor = SalesforceExtractor(data_source)
            
            # Tester la connexion
            if not await self.extractor.test_connection():
                logger.error("Test de connexion extracteur échoué")
                return False
            
            # Découvrir le schéma
            schemas = await self.extractor.discover_schema()
            
            logger.info(f"Schémas découverts: {len(schemas)}")
            
            # Afficher les objets découverts
            for obj_name, schema in schemas.items():
                logger.info(f"  - {obj_name}: {len(schema.fields)} champs, {len(schema.relationships)} relations")
            
            # Vérifier que nos objets custom sont présents
            expected_objects = ['Account', 'Contact', 'Playground__c', 'Reservation__c', 'Equipment__c', 'Maintenance__c']
            found_objects = []
            missing_objects = []
            
            for obj_name in expected_objects:
                if obj_name in schemas:
                    found_objects.append(obj_name)
                else:
                    missing_objects.append(obj_name)
            
            logger.info(f"Objets trouvés: {found_objects}")
            if missing_objects:
                logger.warning(f"Objets manquants: {missing_objects}")
            
            return len(found_objects) >= 2  # Au minimum Account et Contact
            
        except Exception as e:
            logger.error(f"Erreur lors du test de découverte de schéma: {str(e)}")
            return False
    
    async def run_data_extraction_test(self) -> bool:
        """Teste l'extraction de données."""
        try:
            logger.info("Test d'extraction de données")
            
            if not self.extractor:
                logger.error("Extracteur non initialisé")
                return False
            
            # Récupérer les données créées
            created_records = self.test_manager.get_created_records_summary()
            
            extraction_results = {}
            
            # Tester l'extraction pour chaque type d'objet
            for object_type, record_info in created_records.items():
                if record_info['count'] == 0:
                    continue
                
                logger.info(f"Extraction de {object_type} ({record_info['count']} enregistrements créés)")
                
                try:
                    extracted_records = []
                    async for batch in self.extractor.extract_entity_data(object_type, batch_size=100):
                        extracted_records.extend(batch)
                    
                    extraction_results[object_type] = {
                        'created': record_info['count'],
                        'extracted': len(extracted_records),
                        'success': len(extracted_records) >= record_info['count']
                    }
                    
                    logger.info(f"  Résultat: {len(extracted_records)} enregistrements extraits")
                    
                    # Valider quelques enregistrements
                    if extracted_records:
                        sample_record = extracted_records[0]
                        logger.debug(f"  Exemple d'enregistrement: {sample_record.entity_name} - {sample_record.source_id}")
                        logger.debug(f"  Champs: {list(sample_record.data.keys())}")
                
                except Exception as e:
                    logger.error(f"  Erreur lors de l'extraction de {object_type}: {str(e)}")
                    extraction_results[object_type] = {
                        'created': record_info['count'],
                        'extracted': 0,
                        'success': False,
                        'error': str(e)
                    }
            
            # Résumé des résultats
            logger.info("Résumé de l'extraction:")
            total_success = 0
            total_tests = 0
            
            for object_type, result in extraction_results.items():
                status = "✓" if result['success'] else "✗"
                logger.info(f"  {status} {object_type}: {result['extracted']}/{result['created']}")
                if result['success']:
                    total_success += 1
                total_tests += 1
            
            success_rate = (total_success / total_tests * 100) if total_tests > 0 else 0
            logger.info(f"Taux de réussite: {success_rate:.1f}% ({total_success}/{total_tests})")
            
            return success_rate >= 80  # Au moins 80% de réussite
            
        except Exception as e:
            logger.error(f"Erreur lors du test d'extraction: {str(e)}")
            return False
    
    async def cleanup_test_data(self) -> bool:
        """Nettoie les données de test."""
        if self.test_manager:
            logger.info("Nettoyage des données de test")
            return await self.test_manager.cleanup_test_data()
        return True
    
    async def run_complete_test(self) -> bool:
        """Exécute le test complet."""
        try:
            logger.info("Début du test complet Salesforce")
            
            # 1. Connexion
            if not await self.connect_to_salesforce():
                return False
            
            # 2. Configuration de l'environnement
            if not await self.setup_test_environment():
                return False
            
            # 3. Test de découverte de schéma
            if not await self.run_schema_discovery_test():
                logger.error("Test de découverte de schéma échoué")
                return False
            
            # 4. Test d'extraction de données
            if not await self.run_data_extraction_test():
                logger.error("Test d'extraction de données échoué")
                return False
            
            logger.info("Test complet Salesforce réussi ✓")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors du test complet: {str(e)}")
            return False
        
        finally:
            # Nettoyage
            await self.cleanup_test_data()


async def main():
    """Point d'entrée principal."""
    parser = argparse.ArgumentParser(description="Tests d'intégration Salesforce")
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'])
    parser.add_argument('--test-type', default='complete', choices=['complete', 'schema', 'extraction'])
    parser.add_argument('--no-cleanup', action='store_true', help="Ne pas nettoyer les données de test")
    
    args = parser.parse_args()
    
    runner = SalesforceTestRunner()
    runner.setup_logging(args.log_level)
    
    try:
        if args.test_type == 'complete':
            success = await runner.run_complete_test()
        elif args.test_type == 'schema':
            await runner.connect_to_salesforce()
            success = await runner.run_schema_discovery_test()
        elif args.test_type == 'extraction':
            await runner.connect_to_salesforce()
            await runner.setup_test_environment()
            await runner.run_schema_discovery_test()
            success = await runner.run_data_extraction_test()
        
        if not args.no_cleanup:
            await runner.cleanup_test_data()
        
        if success:
            logger.info("Tests terminés avec succès ✓")
            sys.exit(0)
        else:
            logger.error("Tests échoués ✗")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Tests interrompus par l'utilisateur")
        await runner.cleanup_test_data()
        sys.exit(1)
    except Exception as e:
        logger.error(f"Erreur inattendue: {str(e)}")
        await runner.cleanup_test_data()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
