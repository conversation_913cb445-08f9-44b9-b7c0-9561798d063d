"""
Tests unitaires pour le générateur de données de test.

Ces tests valident que le générateur produit des données cohérentes
et réalistes pour les tests Salesforce.
"""

import pytest
from datetime import datetime, date
from tests.salesforce_test_data.test_data_generator import TestDataGenerator
from tests.salesforce_test_data.custom_objects import get_all_object_definitions


class TestDataGenerator:
    """Tests pour le générateur de données de test."""
    
    @pytest.fixture
    def generator(self):
        """Fixture pour le générateur de données."""
        return TestDataGenerator()
    
    def test_generate_accounts(self, generator):
        """Test de génération des comptes."""
        accounts = generator.generate_accounts(5)
        
        assert len(accounts) == 5
        
        for account in accounts:
            # Vérifier les champs obligatoires
            assert 'Name' in account
            assert 'Type' in account
            assert 'BillingCountry' in account
            
            # Vérifier les types
            assert isinstance(account['Name'], str)
            assert len(account['Name']) > 0
            assert account['BillingCountry'] == 'France'
            
            # Vérifier les valeurs valides
            assert account['Type'] in ['Other', 'Customer - Direct']
    
    def test_generate_contacts(self, generator):
        """Test de génération des contacts."""
        account_ids = ['***************', '***************']
        contacts = generator.generate_contacts(10, account_ids)
        
        assert len(contacts) == 10
        
        for contact in contacts:
            # Vérifier les champs obligatoires
            assert 'FirstName' in contact
            assert 'LastName' in contact
            assert 'Email' in contact
            assert 'MailingCountry' in contact
            
            # Vérifier les types
            assert isinstance(contact['FirstName'], str)
            assert isinstance(contact['LastName'], str)
            assert '@' in contact['Email']
            assert contact['MailingCountry'] == 'France'
            
            # Vérifier la date de naissance
            if contact['Birthdate']:
                birthdate = datetime.fromisoformat(contact['Birthdate']).date()
                today = date.today()
                age = today.year - birthdate.year
                assert 18 <= age <= 70  # Age réaliste
        
        # Vérifier que certains contacts sont associés à des comptes
        associated_contacts = [c for c in contacts if c.get('AccountId')]
        assert len(associated_contacts) > 0  # Au moins quelques-uns associés
        
        # Vérifier que les IDs de comptes utilisés sont valides
        for contact in associated_contacts:
            assert contact['AccountId'] in account_ids
    
    def test_generate_playgrounds(self, generator):
        """Test de génération des terrains."""
        account_ids = ['***************', '***************']
        playgrounds = generator.generate_playgrounds(6, account_ids)
        
        assert len(playgrounds) == 6
        
        for playground in playgrounds:
            # Vérifier les champs obligatoires
            assert 'Name' in playground
            assert 'Account__c' in playground
            assert 'Playground_Type__c' in playground
            assert 'Surface_Type__c' in playground
            assert 'Capacity__c' in playground
            assert 'Hourly_Rate__c' in playground
            
            # Vérifier les types
            assert isinstance(playground['Name'], str)
            assert playground['Account__c'] in account_ids
            assert isinstance(playground['Capacity__c'], int)
            assert isinstance(playground['Hourly_Rate__c'], float)
            assert isinstance(playground['Is_Active__c'], bool)
            
            # Vérifier les valeurs valides
            valid_types = ['Football', 'Tennis', 'Basketball', 'Volleyball', 'Multi-sport']
            assert playground['Playground_Type__c'] in valid_types
            
            # Vérifier la cohérence des données
            if playground['Playground_Type__c'] == 'Tennis':
                assert playground['Capacity__c'] <= 4
            elif playground['Playground_Type__c'] == 'Football':
                assert playground['Capacity__c'] >= 18
    
    def test_generate_equipment(self, generator):
        """Test de génération des équipements."""
        playground_ids = ['a00000000000001', 'a00000000000002']
        equipment = generator.generate_equipment(playground_ids, (2, 4))
        
        assert len(equipment) >= 4  # Au moins 2 équipements par terrain
        
        for equip in equipment:
            # Vérifier les champs obligatoires
            assert 'Name' in equip
            assert 'Equipment_Type__c' in equip
            assert 'Playground__c' in equip
            assert 'Quantity_Available__c' in equip
            assert 'Condition__c' in equip
            
            # Vérifier les types
            assert isinstance(equip['Name'], str)
            assert equip['Playground__c'] in playground_ids
            assert isinstance(equip['Quantity_Available__c'], int)
            assert equip['Quantity_Available__c'] > 0
            
            # Vérifier les valeurs valides
            valid_conditions = ['Excellent', 'Bon', 'Acceptable', 'À remplacer']
            assert equip['Condition__c'] in valid_conditions
            
            # Vérifier les dates
            if equip['Purchase_Date__c']:
                purchase_date = datetime.fromisoformat(equip['Purchase_Date__c']).date()
                assert purchase_date <= date.today()
    
    def test_generate_maintenance(self, generator):
        """Test de génération des maintenances."""
        playground_ids = ['a00000000000001', 'a00000000000002']
        maintenance = generator.generate_maintenance(playground_ids, (1, 3))
        
        assert len(maintenance) >= 2  # Au moins 1 maintenance par terrain
        
        for maint in maintenance:
            # Vérifier les champs obligatoires
            assert 'Playground__c' in maint
            assert 'Maintenance_Type__c' in maint
            assert 'Scheduled_Date__c' in maint
            assert 'Status__c' in maint
            assert 'Description__c' in maint
            
            # Vérifier les types
            assert maint['Playground__c'] in playground_ids
            assert isinstance(maint['Description__c'], str)
            assert len(maint['Description__c']) > 0
            
            # Vérifier les valeurs valides
            valid_types = ['Préventive', 'Corrective', 'Urgente', 'Saisonnière', 'Rénovation']
            assert maint['Maintenance_Type__c'] in valid_types
            
            valid_statuses = ['Planifiée', 'En cours', 'Terminée', 'Reportée', 'Annulée']
            assert maint['Status__c'] in valid_statuses
            
            # Vérifier la cohérence des dates
            scheduled_date = datetime.fromisoformat(maint['Scheduled_Date__c']).date()
            if maint['Completed_Date__c']:
                completed_date = datetime.fromisoformat(maint['Completed_Date__c']).date()
                assert completed_date >= scheduled_date
    
    def test_generate_reservations(self, generator):
        """Test de génération des réservations."""
        contact_ids = ['003000000000001', '003000000000002']
        playground_ids = ['a00000000000001', 'a00000000000002']
        reservations = generator.generate_reservations(contact_ids, playground_ids, 20)
        
        assert len(reservations) == 20
        
        for reservation in reservations:
            # Vérifier les champs obligatoires
            assert 'Contact__c' in reservation
            assert 'Playground__c' in reservation
            assert 'Reservation_Date__c' in reservation
            assert 'Start_Time__c' in reservation
            assert 'End_Time__c' in reservation
            assert 'Duration_Hours__c' in reservation
            assert 'Status__c' in reservation
            assert 'Total_Amount__c' in reservation
            
            # Vérifier les types
            assert reservation['Contact__c'] in contact_ids
            assert reservation['Playground__c'] in playground_ids
            assert isinstance(reservation['Duration_Hours__c'], float)
            assert isinstance(reservation['Total_Amount__c'], float)
            assert isinstance(reservation['Number_of_Players__c'], int)
            
            # Vérifier les valeurs valides
            valid_statuses = ['En attente', 'Confirmée', 'En cours', 'Terminée', 'Annulée', 'No-show']
            assert reservation['Status__c'] in valid_statuses
            
            valid_payment_statuses = ['En attente', 'Payé', 'Partiellement payé', 'Remboursé', 'Annulé']
            assert reservation['Payment_Status__c'] in valid_payment_statuses
            
            # Vérifier la cohérence des heures
            start_time = datetime.fromisoformat(reservation['Start_Time__c'])
            end_time = datetime.fromisoformat(reservation['End_Time__c'])
            assert end_time > start_time
            
            # Vérifier la durée
            actual_duration = (end_time - start_time).total_seconds() / 3600
            assert abs(actual_duration - reservation['Duration_Hours__c']) < 0.1  # Tolérance
    
    def test_generate_complete_dataset(self, generator):
        """Test de génération d'un jeu de données complet."""
        dataset = generator.generate_complete_dataset(
            accounts_count=3,
            contacts_count=8,
            playgrounds_count=5,
            reservations_count=15
        )
        
        # Vérifier que tous les types de données sont présents
        expected_keys = ['accounts', 'contacts', 'playgrounds', 'equipment', 'maintenance', 'reservations']
        for key in expected_keys:
            assert key in dataset
            assert isinstance(dataset[key], list)
            assert len(dataset[key]) > 0
        
        # Vérifier les quantités
        assert len(dataset['accounts']) == 3
        assert len(dataset['contacts']) == 8
        assert len(dataset['playgrounds']) == 5
        assert len(dataset['reservations']) == 15
        
        # Vérifier la cohérence entre les objets
        # Les contacts peuvent référencer les comptes
        account_names = {acc['Name'] for acc in dataset['accounts']}
        
        # Les terrains doivent référencer les comptes
        playground_accounts = {pg.get('Account__c') for pg in dataset['playgrounds']}
        # Note: Dans le générateur, nous utilisons des IDs simulés, donc on ne peut pas vérifier la cohérence exacte
        
        # Les équipements doivent référencer les terrains
        equipment_playgrounds = {eq.get('Playground__c') for eq in dataset['equipment']}
        # Note: Même limitation avec les IDs simulés
    
    def test_data_consistency(self, generator):
        """Test de cohérence des données générées."""
        # Générer plusieurs fois pour vérifier la reproductibilité
        dataset1 = generator.generate_complete_dataset(accounts_count=2, contacts_count=3, playgrounds_count=2, reservations_count=5)
        dataset2 = generator.generate_complete_dataset(accounts_count=2, contacts_count=3, playgrounds_count=2, reservations_count=5)
        
        # Avec la même seed, les données devraient être identiques
        assert len(dataset1['accounts']) == len(dataset2['accounts'])
        assert len(dataset1['contacts']) == len(dataset2['contacts'])
        
        # Vérifier que les noms sont cohérents (même seed)
        account_names_1 = [acc['Name'] for acc in dataset1['accounts']]
        account_names_2 = [acc['Name'] for acc in dataset2['accounts']]
        assert account_names_1 == account_names_2
    
    def test_custom_objects_definitions(self):
        """Test de validation des définitions d'objets custom."""
        objects = get_all_object_definitions()
        
        # Vérifier que tous les objets attendus sont présents
        expected_objects = ['Playground__c', 'Reservation__c', 'Equipment__c', 'Maintenance__c']
        for obj_name in expected_objects:
            assert obj_name in objects
        
        # Vérifier la structure des objets
        for obj_name, obj_def in objects.items():
            assert obj_def.api_name == obj_name
            assert obj_def.label is not None
            assert obj_def.plural_label is not None
            assert obj_def.description is not None
            assert len(obj_def.fields) > 0
            
            # Vérifier que chaque champ a les propriétés requises
            for field in obj_def.fields:
                assert field.name is not None
                assert field.type is not None
                assert field.label is not None
