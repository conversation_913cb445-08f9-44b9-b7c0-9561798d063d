"""
Generic data models for FungAI Backend.

This module defines the universal data structures that can represent
data from any source system while preserving metadata and relationships.
"""

from typing import Any, Dict, List, Optional, Union, Set
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, validator
from uuid import uuid4, UUID


class DataType(str, Enum):
    """Supported data types in the generic model."""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    DATE = "date"
    DATETIME = "datetime"
    JSON = "json"
    BINARY = "binary"
    REFERENCE = "reference"  # For relationships


class RelationType(str, Enum):
    """Types of relationships between entities."""
    ONE_TO_ONE = "one_to_one"
    ONE_TO_MANY = "one_to_many"
    MANY_TO_ONE = "many_to_one"
    MANY_TO_MANY = "many_to_many"
    LOOKUP = "lookup"
    MASTER_DETAIL = "master_detail"


class SourceType(str, Enum):
    """Supported source system types."""
    SALESFORCE = "salesforce"
    ORACLE = "oracle"
    SQLSERVER = "sqlserver"
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    API = "api"
    FILE = "file"


class FieldMetadata(BaseModel):
    """Metadata for a field in the generic model."""
    
    name: str = Field(..., description="Field name")
    data_type: DataType = Field(..., description="Data type of the field")
    is_required: bool = Field(default=False, description="Whether field is required")
    is_unique: bool = Field(default=False, description="Whether field values are unique")
    is_indexed: bool = Field(default=False, description="Whether field is indexed")
    is_sensitive: bool = Field(default=False, description="Whether field contains sensitive data")
    max_length: Optional[int] = Field(default=None, description="Maximum length for string fields")
    default_value: Optional[Any] = Field(default=None, description="Default value")
    description: Optional[str] = Field(default=None, description="Field description")
    
    # Source-specific metadata
    source_name: Optional[str] = Field(default=None, description="Original field name in source")
    source_type: Optional[str] = Field(default=None, description="Original field type in source")
    source_metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional source metadata")


class RelationshipMetadata(BaseModel):
    """Metadata for relationships between entities."""
    
    id: UUID = Field(default_factory=uuid4, description="Unique relationship ID")
    name: str = Field(..., description="Relationship name")
    relation_type: RelationType = Field(..., description="Type of relationship")
    source_entity: str = Field(..., description="Source entity name")
    target_entity: str = Field(..., description="Target entity name")
    source_field: str = Field(..., description="Source field name")
    target_field: str = Field(..., description="Target field name")
    is_cascade_delete: bool = Field(default=False, description="Whether delete cascades")
    description: Optional[str] = Field(default=None, description="Relationship description")
    
    # Source-specific metadata
    source_metadata: Dict[str, Any] = Field(default_factory=dict, description="Source-specific relationship data")


class EntitySchema(BaseModel):
    """Schema definition for an entity in the generic model."""
    
    name: str = Field(..., description="Entity name")
    source_type: SourceType = Field(..., description="Source system type")
    source_name: str = Field(..., description="Original entity name in source")
    description: Optional[str] = Field(default=None, description="Entity description")
    
    # Fields
    fields: Dict[str, FieldMetadata] = Field(default_factory=dict, description="Entity fields")
    primary_key: List[str] = Field(default_factory=list, description="Primary key field names")
    
    # Relationships
    relationships: List[RelationshipMetadata] = Field(default_factory=list, description="Entity relationships")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Schema creation time")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Schema last update time")
    version: str = Field(default="1.0", description="Schema version")
    
    # Source-specific metadata
    source_metadata: Dict[str, Any] = Field(default_factory=dict, description="Source-specific entity metadata")
    
    # Security and privacy
    is_sensitive: bool = Field(default=False, description="Whether entity contains sensitive data")
    access_level: str = Field(default="public", description="Access level for the entity")
    
    @validator("fields")
    def validate_primary_key_fields(cls, v, values):
        """Ensure primary key fields exist in the field definitions."""
        if "primary_key" in values:
            for pk_field in values["primary_key"]:
                if pk_field not in v:
                    raise ValueError(f"Primary key field '{pk_field}' not found in field definitions")
        return v


class GenericRecord(BaseModel):
    """A generic record that can represent data from any source."""
    
    id: UUID = Field(default_factory=uuid4, description="Unique record ID")
    entity_name: str = Field(..., description="Name of the entity this record belongs to")
    source_type: SourceType = Field(..., description="Source system type")
    source_id: str = Field(..., description="Original record ID in source system")
    
    # Data
    data: Dict[str, Any] = Field(default_factory=dict, description="Record data")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Record creation time")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Record last update time")
    source_created_at: Optional[datetime] = Field(default=None, description="Creation time in source system")
    source_updated_at: Optional[datetime] = Field(default=None, description="Last update time in source system")
    
    # Ownership and permissions (for Salesforce-style security)
    owner_id: Optional[str] = Field(default=None, description="Record owner ID")
    created_by_id: Optional[str] = Field(default=None, description="Created by user ID")
    last_modified_by_id: Optional[str] = Field(default=None, description="Last modified by user ID")
    
    # Processing metadata
    extraction_batch_id: Optional[UUID] = Field(default=None, description="Extraction batch ID")
    is_processed: bool = Field(default=False, description="Whether record has been processed for vectorization")
    processing_errors: List[str] = Field(default_factory=list, description="Processing errors")
    
    # Source-specific metadata
    source_metadata: Dict[str, Any] = Field(default_factory=dict, description="Source-specific record metadata")
    
    def get_field_value(self, field_name: str) -> Any:
        """Get value of a specific field."""
        return self.data.get(field_name)
    
    def set_field_value(self, field_name: str, value: Any) -> None:
        """Set value of a specific field."""
        self.data[field_name] = value
        self.updated_at = datetime.utcnow()
    
    def has_field(self, field_name: str) -> bool:
        """Check if record has a specific field."""
        return field_name in self.data


class DataSource(BaseModel):
    """Configuration for a data source."""
    
    id: UUID = Field(default_factory=uuid4, description="Unique source ID")
    name: str = Field(..., description="Source name")
    source_type: SourceType = Field(..., description="Source system type")
    description: Optional[str] = Field(default=None, description="Source description")
    
    # Connection configuration
    connection_config: Dict[str, Any] = Field(default_factory=dict, description="Connection configuration")
    
    # Extraction configuration
    extraction_config: Dict[str, Any] = Field(default_factory=dict, description="Extraction configuration")
    
    # Security configuration
    is_enabled: bool = Field(default=True, description="Whether source is enabled")
    is_sensitive: bool = Field(default=False, description="Whether source contains sensitive data")
    excluded_entities: Set[str] = Field(default_factory=set, description="Entities to exclude from extraction")
    excluded_fields: Dict[str, Set[str]] = Field(default_factory=dict, description="Fields to exclude per entity")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Source creation time")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Source last update time")
    last_extraction_at: Optional[datetime] = Field(default=None, description="Last extraction time")
    
    # Schema
    entities: Dict[str, EntitySchema] = Field(default_factory=dict, description="Entity schemas")
    
    def add_entity_schema(self, schema: EntitySchema) -> None:
        """Add an entity schema to this source."""
        self.entities[schema.name] = schema
        self.updated_at = datetime.utcnow()
    
    def get_entity_schema(self, entity_name: str) -> Optional[EntitySchema]:
        """Get schema for a specific entity."""
        return self.entities.get(entity_name)
    
    def is_field_excluded(self, entity_name: str, field_name: str) -> bool:
        """Check if a field is excluded from extraction."""
        return field_name in self.excluded_fields.get(entity_name, set())


class ExtractionBatch(BaseModel):
    """Metadata for an extraction batch."""
    
    id: UUID = Field(default_factory=uuid4, description="Unique batch ID")
    source_id: UUID = Field(..., description="Source ID")
    source_name: str = Field(..., description="Source name")
    
    # Batch metadata
    started_at: datetime = Field(default_factory=datetime.utcnow, description="Batch start time")
    completed_at: Optional[datetime] = Field(default=None, description="Batch completion time")
    status: str = Field(default="running", description="Batch status")
    
    # Statistics
    total_entities: int = Field(default=0, description="Total entities to extract")
    processed_entities: int = Field(default=0, description="Entities processed")
    total_records: int = Field(default=0, description="Total records extracted")
    error_count: int = Field(default=0, description="Number of errors")
    
    # Configuration
    extraction_config: Dict[str, Any] = Field(default_factory=dict, description="Extraction configuration used")
    
    # Errors and logs
    errors: List[str] = Field(default_factory=list, description="Extraction errors")
    logs: List[str] = Field(default_factory=list, description="Extraction logs")
    
    def mark_completed(self) -> None:
        """Mark batch as completed."""
        self.completed_at = datetime.utcnow()
        self.status = "completed"
    
    def mark_failed(self, error: str) -> None:
        """Mark batch as failed."""
        self.completed_at = datetime.utcnow()
        self.status = "failed"
        self.errors.append(error)
    
    def add_error(self, error: str) -> None:
        """Add an error to the batch."""
        self.errors.append(error)
        self.error_count += 1
    
    def add_log(self, message: str) -> None:
        """Add a log message to the batch."""
        timestamp = datetime.utcnow().isoformat()
        self.logs.append(f"[{timestamp}] {message}")
