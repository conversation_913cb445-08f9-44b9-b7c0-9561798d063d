"""
Tests d'intégration Salesforce pour valider l'extraction et le modèle générique.

Ce module teste l'ensemble du processus :
1. Création de données de test dans Salesforce
2. Extraction via notre extracteur Salesforce
3. Validation que le modèle générique correspond aux données créées
"""

import pytest
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger
from simple_salesforce import Salesforce

from src.core.models import DataSource, SourceType, GenericRecord, EntitySchema
from src.extractors.salesforce_extractor import SalesforceExtractor
from tests.salesforce_test_data.salesforce_test_manager import SalesforceTestManager
from tests.salesforce_test_data.test_data_generator import TestDataGenerator


class TestSalesforceIntegration:
    """
    Tests d'intégration complets pour Salesforce.
    
    Ces tests valident que notre modèle générique peut correctement
    représenter et extraire des données Salesforce réelles.
    """
    
    @pytest.fixture(scope="class")
    def sf_connection(self):
        """
        Fixture pour la connexion Salesforce.
        
        Note: Cette fixture nécessite des variables d'environnement
        ou un fichier de configuration pour les credentials Salesforce.
        """
        # En mode test, nous pouvons utiliser un org de développement
        # ou un sandbox Salesforce dédié aux tests
        
        # Pour les tests unitaires, nous pouvons mocker cette connexion
        pytest.skip("Connexion Salesforce réelle requise - configurez les credentials")
        
        # Exemple de configuration réelle :
        # return Salesforce(
        #     username=os.getenv('SF_USERNAME'),
        #     password=os.getenv('SF_PASSWORD'),
        #     security_token=os.getenv('SF_SECURITY_TOKEN'),
        #     domain='test'  # Pour sandbox
        # )
    
    @pytest.fixture(scope="class")
    def test_manager(self, sf_connection):
        """Fixture pour le gestionnaire de test Salesforce."""
        return SalesforceTestManager(sf_connection)
    
    @pytest.fixture(scope="class")
    def data_generator(self):
        """Fixture pour le générateur de données de test."""
        return TestDataGenerator()
    
    @pytest.fixture(scope="class")
    def data_source_config(self):
        """Configuration de la source de données Salesforce pour les tests."""
        return DataSource(
            name="Test Salesforce Org",
            source_type=SourceType.SALESFORCE,
            description="Organisation Salesforce de test pour validation du modèle générique",
            connection_config={
                'username': '<EMAIL>',
                'password': 'test_password',
                'security_token': 'test_token',
                'domain': 'test',
                'api_version': '58.0'
            },
            extraction_config={
                'batch_size': 1000,
                'use_bulk_api': True
            },
            # Exclure certains objets système pour les tests
            excluded_entities={
                'AsyncApexJob', 'ApexLog', 'LoginHistory', 'SetupAuditTrail'
            },
            # Exclure certains champs sensibles
            excluded_fields={
                'Contact': {'SSN__c', 'CreditCard__c'},
                'User': {'Password', 'SecurityToken'}
            }
        )
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_complete_workflow(
        self, 
        sf_connection, 
        test_manager, 
        data_generator, 
        data_source_config
    ):
        """
        Test complet du workflow d'extraction Salesforce.
        
        Ce test valide l'ensemble du processus :
        1. Création des données de test
        2. Extraction via notre extracteur
        3. Validation du modèle générique
        4. Nettoyage des données
        """
        logger.info("Début du test d'intégration Salesforce complet")
        
        try:
            # 1. Configurer l'environnement de test
            logger.info("Configuration de l'environnement de test")
            setup_success = await test_manager.setup_test_environment()
            assert setup_success, "La configuration de l'environnement de test a échoué"
            
            # 2. Créer l'extracteur Salesforce
            logger.info("Création de l'extracteur Salesforce")
            extractor = SalesforceExtractor(data_source_config)
            
            # 3. Tester la connexion
            logger.info("Test de la connexion Salesforce")
            connection_success = await extractor.test_connection()
            assert connection_success, "La connexion à Salesforce a échoué"
            
            # 4. Découvrir le schéma
            logger.info("Découverte du schéma Salesforce")
            schemas = await extractor.discover_schema()
            assert len(schemas) > 0, "Aucun schéma découvert"
            
            # Vérifier que nos objets custom sont présents
            expected_objects = ['Account', 'Contact', 'Playground__c', 'Reservation__c', 'Equipment__c', 'Maintenance__c']
            for obj_name in expected_objects:
                assert obj_name in schemas, f"Objet {obj_name} non trouvé dans le schéma"
            
            # 5. Valider les schémas découverts
            await self._validate_discovered_schemas(schemas)
            
            # 6. Extraire et valider les données
            await self._validate_data_extraction(extractor, test_manager)
            
            logger.info("Test d'intégration Salesforce terminé avec succès")
            
        finally:
            # 7. Nettoyer les données de test
            logger.info("Nettoyage des données de test")
            await test_manager.cleanup_test_data()
    
    async def _validate_discovered_schemas(self, schemas: Dict[str, EntitySchema]):
        """Valide que les schémas découverts sont corrects."""
        logger.info("Validation des schémas découverts")
        
        # Valider le schéma Account
        account_schema = schemas.get('Account')
        assert account_schema is not None, "Schéma Account manquant"
        assert account_schema.source_type == SourceType.SALESFORCE
        assert 'Name' in account_schema.fields
        assert 'Id' in account_schema.fields
        assert account_schema.primary_key == ['Id']
        
        # Valider le schéma Contact
        contact_schema = schemas.get('Contact')
        assert contact_schema is not None, "Schéma Contact manquant"
        assert 'FirstName' in contact_schema.fields
        assert 'LastName' in contact_schema.fields
        assert 'Email' in contact_schema.fields
        
        # Valider le schéma Playground__c
        playground_schema = schemas.get('Playground__c')
        assert playground_schema is not None, "Schéma Playground__c manquant"
        assert 'Name' in playground_schema.fields
        assert 'Account__c' in playground_schema.fields
        assert 'Playground_Type__c' in playground_schema.fields
        assert 'Hourly_Rate__c' in playground_schema.fields
        
        # Vérifier les relations
        playground_relationships = playground_schema.relationships
        account_relationship = next(
            (rel for rel in playground_relationships if rel.target_entity == 'Account'), 
            None
        )
        assert account_relationship is not None, "Relation Playground -> Account manquante"
        
        # Valider le schéma Reservation__c
        reservation_schema = schemas.get('Reservation__c')
        assert reservation_schema is not None, "Schéma Reservation__c manquant"
        assert 'Contact__c' in reservation_schema.fields
        assert 'Playground__c' in reservation_schema.fields
        assert 'Start_Time__c' in reservation_schema.fields
        assert 'End_Time__c' in reservation_schema.fields
        assert 'Total_Amount__c' in reservation_schema.fields
        
        # Vérifier les types de champs
        start_time_field = reservation_schema.fields['Start_Time__c']
        assert start_time_field.data_type.value == 'datetime'
        
        total_amount_field = reservation_schema.fields['Total_Amount__c']
        assert total_amount_field.data_type.value == 'float'  # Currency -> Float
        
        logger.info("Validation des schémas terminée avec succès")
    
    async def _validate_data_extraction(self, extractor: SalesforceExtractor, test_manager: SalesforceTestManager):
        """Valide l'extraction des données."""
        logger.info("Validation de l'extraction des données")
        
        # Récupérer le résumé des données créées
        created_records = test_manager.get_created_records_summary()
        
        # Tester l'extraction pour chaque type d'objet
        for object_type, record_info in created_records.items():
            if record_info['count'] == 0:
                continue
                
            logger.info(f"Test d'extraction pour {object_type}")
            
            # Extraire les données
            extracted_records = []
            async for batch in extractor.extract_entity_data(object_type, batch_size=100):
                extracted_records.extend(batch)
            
            # Vérifier que nous avons extrait au moins autant d'enregistrements que créés
            assert len(extracted_records) >= record_info['count'], \
                f"Extraction incomplète pour {object_type}: {len(extracted_records)} < {record_info['count']}"
            
            # Valider la structure des enregistrements extraits
            await self._validate_extracted_records(object_type, extracted_records, record_info['ids'])
        
        logger.info("Validation de l'extraction terminée avec succès")
    
    async def _validate_extracted_records(
        self, 
        object_type: str, 
        extracted_records: List[GenericRecord], 
        created_ids: List[str]
    ):
        """Valide la structure et le contenu des enregistrements extraits."""
        logger.info(f"Validation des enregistrements extraits pour {object_type}")
        
        # Vérifier qu'au moins un enregistrement a été extrait
        assert len(extracted_records) > 0, f"Aucun enregistrement extrait pour {object_type}"
        
        # Valider la structure générique
        for record in extracted_records[:5]:  # Valider les 5 premiers
            # Vérifier les champs obligatoires du modèle générique
            assert record.entity_name == object_type
            assert record.source_type == SourceType.SALESFORCE
            assert record.source_id is not None and record.source_id != ""
            assert isinstance(record.data, dict)
            assert len(record.data) > 0
            
            # Vérifier que l'ID Salesforce est présent
            assert 'Id' in record.data, f"Champ Id manquant dans {object_type}"
            
            # Vérifications spécifiques par type d'objet
            if object_type == 'Account':
                assert 'Name' in record.data, "Champ Name manquant dans Account"
                assert record.data['Name'] is not None
                
            elif object_type == 'Contact':
                assert 'LastName' in record.data, "Champ LastName manquant dans Contact"
                assert record.data['LastName'] is not None
                
            elif object_type == 'Playground__c':
                assert 'Name' in record.data, "Champ Name manquant dans Playground__c"
                assert 'Account__c' in record.data, "Champ Account__c manquant dans Playground__c"
                assert 'Playground_Type__c' in record.data, "Champ Playground_Type__c manquant"
                assert 'Hourly_Rate__c' in record.data, "Champ Hourly_Rate__c manquant"
                
            elif object_type == 'Reservation__c':
                assert 'Contact__c' in record.data, "Champ Contact__c manquant dans Reservation__c"
                assert 'Playground__c' in record.data, "Champ Playground__c manquant dans Reservation__c"
                assert 'Start_Time__c' in record.data, "Champ Start_Time__c manquant"
                assert 'Total_Amount__c' in record.data, "Champ Total_Amount__c manquant"
                
            # Vérifier les métadonnées Salesforce
            if record.source_created_at:
                assert isinstance(record.source_created_at, datetime)
            if record.source_updated_at:
                assert isinstance(record.source_updated_at, datetime)
            
            # Vérifier les champs de propriété Salesforce
            if 'OwnerId' in record.data:
                assert record.owner_id == record.data['OwnerId']
            if 'CreatedById' in record.data:
                assert record.created_by_id == record.data['CreatedById']
        
        # Vérifier que nos enregistrements créés sont présents
        extracted_ids = {record.source_id for record in extracted_records}
        for created_id in created_ids:
            assert created_id in extracted_ids, \
                f"Enregistrement créé {created_id} non trouvé dans l'extraction de {object_type}"
        
        logger.info(f"Validation des enregistrements {object_type} terminée avec succès")
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_schema_discovery_performance(self, data_source_config):
        """Test de performance de la découverte de schéma."""
        extractor = SalesforceExtractor(data_source_config)
        
        start_time = datetime.now()
        schemas = await extractor.discover_schema()
        end_time = datetime.now()
        
        discovery_time = (end_time - start_time).total_seconds()
        
        # La découverte ne devrait pas prendre plus de 2 minutes
        assert discovery_time < 120, f"Découverte de schéma trop lente: {discovery_time}s"
        assert len(schemas) > 0, "Aucun schéma découvert"
        
        logger.info(f"Découverte de {len(schemas)} schémas en {discovery_time:.2f}s")
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_data_extraction_performance(self, data_source_config):
        """Test de performance de l'extraction de données."""
        extractor = SalesforceExtractor(data_source_config)
        
        # Tester l'extraction d'un objet standard avec beaucoup de données
        start_time = datetime.now()
        
        record_count = 0
        async for batch in extractor.extract_entity_data('User', batch_size=1000):
            record_count += len(batch)
            # Limiter le test à 5000 enregistrements max
            if record_count >= 5000:
                break
        
        end_time = datetime.now()
        extraction_time = (end_time - start_time).total_seconds()
        
        if record_count > 0:
            records_per_second = record_count / extraction_time
            logger.info(f"Extraction de {record_count} enregistrements en {extraction_time:.2f}s ({records_per_second:.1f} rec/s)")
            
            # Performance minimale attendue : 100 enregistrements/seconde
            assert records_per_second >= 100, f"Performance d'extraction insuffisante: {records_per_second:.1f} rec/s"
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_error_handling(self, data_source_config):
        """Test de la gestion d'erreurs."""
        # Test avec des credentials invalides
        invalid_config = data_source_config.copy()
        invalid_config.connection_config['password'] = 'invalid_password'
        
        extractor = SalesforceExtractor(invalid_config)
        
        # La connexion devrait échouer
        connection_success = await extractor.test_connection()
        assert not connection_success, "La connexion devrait échouer avec des credentials invalides"
        
        # Test avec un objet inexistant
        valid_extractor = SalesforceExtractor(data_source_config)
        
        with pytest.raises(Exception):
            async for batch in valid_extractor.extract_entity_data('NonExistentObject__c'):
                pass
