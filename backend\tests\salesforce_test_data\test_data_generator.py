"""
Générateur de données de test pour Salesforce.

Ce module génère des données de test réalistes et cohérentes
pour valider l'extraction et le modèle générique.
"""

import random
from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Tuple
from faker import Faker
from loguru import logger


class TestDataGenerator:
    """
    Générateur de données de test réalistes pour le système de réservation.
    
    Utilise la bibliothèque Faker pour générer des données cohérentes
    et réalistes en français.
    """
    
    def __init__(self, locale: str = 'fr_FR'):
        """
        Initialise le générateur de données.
        
        Args:
            locale: Locale pour la génération des données (défaut: français)
        """
        self.fake = Faker(locale)
        Faker.seed(42)  # Pour des résultats reproductibles
        random.seed(42)
    
    def generate_accounts(self, count: int = 5) -> List[Dict[str, Any]]:
        """
        Génère des comptes (organisations) de test.
        
        Args:
            count: Nombre de comptes à générer
            
        Returns:
            Liste des données de comptes
        """
        account_types = [
            ('Club Sportif Municipal', 'Other', 'Municipalité'),
            ('Tennis Club Elite', 'Customer - Direct', 'Club privé'),
            ('Centre Sportif Universitaire', 'Other', 'Institution'),
            ('Association Quartier', 'Other', 'Association'),
            ('Complexe Sportif Privé', 'Customer - Direct', 'Entreprise privée')
        ]
        
        accounts = []
        
        for i in range(min(count, len(account_types))):
            name_template, sf_type, description_type = account_types[i]
            
            # Générer des variations du nom
            if 'Club' in name_template:
                name = f"{name_template} {self.fake.city()}"
            elif 'Centre' in name_template:
                name = f"{name_template} de {self.fake.city()}"
            elif 'Association' in name_template:
                name = f"{name_template} {self.fake.street_name()}"
            elif 'Complexe' in name_template:
                name = f"{name_template} {self.fake.company_suffix()}"
            else:
                name = name_template
            
            account = {
                'Name': name,
                'Type': sf_type,
                'Phone': self.fake.phone_number(),
                'Website': f"https://{self.fake.domain_name()}",
                'Description': f"{description_type} gérant des installations sportives - {self.fake.catch_phrase()}",
                'BillingStreet': self.fake.street_address(),
                'BillingCity': self.fake.city(),
                'BillingPostalCode': self.fake.postcode(),
                'BillingCountry': 'France'
            }
            
            accounts.append(account)
        
        # Générer des comptes supplémentaires si nécessaire
        for i in range(len(account_types), count):
            account = {
                'Name': f"Club Sportif {self.fake.city()}",
                'Type': random.choice(['Other', 'Customer - Direct']),
                'Phone': self.fake.phone_number(),
                'Website': f"https://{self.fake.domain_name()}",
                'Description': f"Organisation sportive - {self.fake.catch_phrase()}",
                'BillingStreet': self.fake.street_address(),
                'BillingCity': self.fake.city(),
                'BillingPostalCode': self.fake.postcode(),
                'BillingCountry': 'France'
            }
            accounts.append(account)
        
        return accounts
    
    def generate_contacts(self, count: int = 15, account_ids: List[str] = None) -> List[Dict[str, Any]]:
        """
        Génère des contacts de test.
        
        Args:
            count: Nombre de contacts à générer
            account_ids: Liste des IDs de comptes pour les associations
            
        Returns:
            Liste des données de contacts
        """
        contacts = []
        
        for i in range(count):
            # 30% des contacts sont associés à un compte
            account_id = None
            if account_ids and random.random() < 0.3:
                account_id = random.choice(account_ids)
            
            contact = {
                'FirstName': self.fake.first_name(),
                'LastName': self.fake.last_name(),
                'Email': self.fake.email(),
                'Phone': self.fake.phone_number(),
                'Birthdate': self.fake.date_of_birth(minimum_age=18, maximum_age=70).isoformat(),
                'AccountId': account_id,
                'MailingStreet': self.fake.street_address(),
                'MailingCity': self.fake.city(),
                'MailingPostalCode': self.fake.postcode(),
                'MailingCountry': 'France',
                'Description': self._generate_contact_description()
            }
            
            contacts.append(contact)
        
        return contacts
    
    def _generate_contact_description(self) -> str:
        """Génère une description réaliste pour un contact."""
        sports = ['football', 'tennis', 'basketball', 'volleyball', 'badminton', 'pétanque']
        levels = ['débutant', 'intermédiaire', 'confirmé', 'expert']
        frequencies = ['occasionnel', 'régulier', 'assidu', 'professionnel']
        
        sport = random.choice(sports)
        level = random.choice(levels)
        frequency = random.choice(frequencies)
        
        descriptions = [
            f"Pratiquant {frequency} de {sport}, niveau {level}",
            f"Passionné de {sport}, joue de manière {frequency}",
            f"Joueur de {sport} {level}, pratique {frequency}",
            f"Amateur de {sport}, niveau {level}, participation {frequency}"
        ]
        
        return random.choice(descriptions)
    
    def generate_playgrounds(self, count: int = 8, account_ids: List[str] = None) -> List[Dict[str, Any]]:
        """
        Génère des terrains de test.
        
        Args:
            count: Nombre de terrains à générer
            account_ids: Liste des IDs de comptes propriétaires
            
        Returns:
            Liste des données de terrains
        """
        if not account_ids:
            raise ValueError("Les IDs de comptes sont requis pour créer les terrains")
        
        playground_types = ['Football', 'Tennis', 'Basketball', 'Volleyball', 'Multi-sport']
        surface_types = {
            'Football': ['Gazon naturel', 'Gazon synthétique', 'Terre'],
            'Tennis': ['Terre battue', 'Gazon synthétique', 'Béton'],
            'Basketball': ['Béton', 'Parquet'],
            'Volleyball': ['Sable', 'Parquet', 'Béton'],
            'Multi-sport': ['Parquet', 'Gazon synthétique', 'Béton']
        }
        
        capacities = {
            'Football': (18, 22),
            'Tennis': (2, 4),
            'Basketball': (8, 12),
            'Volleyball': (8, 12),
            'Multi-sport': (15, 25)
        }
        
        hourly_rates = {
            'Football': (40, 80),
            'Tennis': (20, 50),
            'Basketball': (15, 35),
            'Volleyball': (20, 40),
            'Multi-sport': (30, 60)
        }
        
        playgrounds = []
        
        for i in range(count):
            playground_type = random.choice(playground_types)
            surface_type = random.choice(surface_types[playground_type])
            capacity = random.randint(*capacities[playground_type])
            hourly_rate = random.uniform(*hourly_rates[playground_type])
            
            # Générer un nom de terrain
            terrain_names = [
                f"Terrain {playground_type} {i+1}",
                f"Court {playground_type} Principal",
                f"Terrain {playground_type} {random.choice(['Nord', 'Sud', 'Est', 'Ouest'])}",
                f"Court {playground_type} {random.choice(['A', 'B', 'C', 'Central'])}"
            ]
            
            equipment_options = {
                'Football': ['Ballons', 'Filets', 'Éclairage', 'Vestiaires', 'Douches', 'Gradins'],
                'Tennis': ['Filets', 'Raquettes', 'Éclairage', 'Vestiaires'],
                'Basketball': ['Ballons', 'Tableau de score', 'Éclairage', 'Gradins'],
                'Volleyball': ['Filets', 'Ballons', 'Éclairage', 'Vestiaires'],
                'Multi-sport': ['Ballons', 'Filets', 'Raquettes', 'Éclairage', 'Vestiaires', 'Douches', 'Gradins']
            }
            
            # Sélectionner aléatoirement des équipements
            available_equipment = random.sample(
                equipment_options[playground_type], 
                k=random.randint(2, len(equipment_options[playground_type]))
            )
            
            playground = {
                'Name': random.choice(terrain_names),
                'Account__c': random.choice(account_ids),
                'Playground_Type__c': playground_type,
                'Surface_Type__c': surface_type,
                'Capacity__c': capacity,
                'Hourly_Rate__c': round(hourly_rate, 2),
                'Is_Active__c': random.choice([True, True, True, False]),  # 75% actifs
                'Equipment_Available__c': ';'.join(available_equipment),
                'Description__c': self._generate_playground_description(playground_type, surface_type),
                'Opening_Hours__c': self._generate_opening_hours(),
                'Special_Rules__c': self._generate_special_rules(playground_type)
            }
            
            playgrounds.append(playground)
        
        return playgrounds
    
    def _generate_playground_description(self, playground_type: str, surface_type: str) -> str:
        """Génère une description réaliste pour un terrain."""
        descriptions = [
            f"Terrain de {playground_type.lower()} avec surface en {surface_type.lower()}",
            f"Excellent terrain de {playground_type.lower()} sur {surface_type.lower()}",
            f"Terrain {playground_type.lower()} professionnel, surface {surface_type.lower()}",
            f"Installation de {playground_type.lower()} moderne avec {surface_type.lower()}"
        ]
        
        features = [
            "éclairage LED",
            "vestiaires modernes",
            "accès handicapés",
            "parking gratuit",
            "vue dégagée",
            "environnement calme"
        ]
        
        base_desc = random.choice(descriptions)
        feature = random.choice(features)
        
        return f"{base_desc}. Équipé de {feature}."
    
    def _generate_opening_hours(self) -> str:
        """Génère des heures d'ouverture réalistes."""
        opening_patterns = [
            "8h00-22h00",
            "7h00-23h00",
            "6h00-22h00",
            "9h00-21h00",
            "8h00-20h00 (hiver), 8h00-22h00 (été)"
        ]
        
        return random.choice(opening_patterns)
    
    def _generate_special_rules(self, playground_type: str) -> str:
        """Génère des règles spéciales selon le type de terrain."""
        rules_by_type = {
            'Football': [
                "Chaussures à crampons interdites sur terrain synthétique",
                "Maximum 22 joueurs simultanés",
                "Réservation minimum 1h30"
            ],
            'Tennis': [
                "Réservation minimum 1h, maximum 3h consécutives",
                "Chaussures de tennis obligatoires",
                "Balles fournies sur demande"
            ],
            'Basketball': [
                "Accès libre en journée, réservation obligatoire le soir",
                "Maximum 12 joueurs",
                "Chaussures de sport obligatoires"
            ],
            'Volleyball': [
                "Filet ajustable en hauteur",
                "Réservation par créneaux de 2h",
                "Balles fournies"
            ],
            'Multi-sport': [
                "Chaussures de sport obligatoires, pas de chaussures de ville",
                "Configuration adaptable selon le sport",
                "Réservation avec précision du sport pratiqué"
            ]
        }
        
        type_rules = rules_by_type.get(playground_type, ["Respecter les horaires de réservation"])
        return random.choice(type_rules)
    
    def generate_equipment(self, playground_ids: List[str], count_per_playground: Tuple[int, int] = (1, 4)) -> List[Dict[str, Any]]:
        """
        Génère des équipements pour les terrains.
        
        Args:
            playground_ids: Liste des IDs de terrains
            count_per_playground: Tuple (min, max) d'équipements par terrain
            
        Returns:
            Liste des données d'équipements
        """
        equipment_types = [
            'Ballon Football', 'Ballon Basketball', 'Ballon Volleyball',
            'Raquette Tennis', 'Raquette Badminton', 'Filet Tennis', 'Filet Volleyball',
            'Éclairage', 'Tableau de Score', 'Sifflet', 'Plots', 'Cônes'
        ]
        
        conditions = ['Excellent', 'Bon', 'Acceptable', 'À remplacer']
        
        equipment_list = []
        
        for playground_id in playground_ids:
            equipment_count = random.randint(*count_per_playground)
            
            for _ in range(equipment_count):
                equipment_type = random.choice(equipment_types)
                condition = random.choice(conditions)
                
                # Générer des quantités réalistes selon le type
                if 'Ballon' in equipment_type:
                    quantity = random.randint(3, 15)
                elif 'Raquette' in equipment_type:
                    quantity = random.randint(4, 12)
                elif 'Filet' in equipment_type:
                    quantity = random.randint(1, 3)
                else:
                    quantity = random.randint(1, 5)
                
                # Date d'achat dans les 2 dernières années
                purchase_date = self.fake.date_between(start_date='-2y', end_date='today')
                
                # Dernière maintenance (optionnelle)
                last_maintenance = None
                if random.random() < 0.6:  # 60% ont eu une maintenance
                    last_maintenance = self.fake.date_between(start_date=purchase_date, end_date='today')
                
                # Tarif de location (optionnel)
                rental_rate = None
                if equipment_type in ['Raquette Tennis', 'Raquette Badminton'] and random.random() < 0.7:
                    rental_rate = round(random.uniform(3.0, 10.0), 2)
                
                equipment = {
                    'Name': f"{equipment_type} - {self.fake.word().title()}",
                    'Equipment_Type__c': equipment_type,
                    'Playground__c': playground_id,
                    'Quantity_Available__c': quantity,
                    'Condition__c': condition,
                    'Purchase_Date__c': purchase_date.isoformat(),
                    'Last_Maintenance__c': last_maintenance.isoformat() if last_maintenance else None,
                    'Is_Available__c': condition != 'Hors service',
                    'Rental_Rate__c': rental_rate
                }
                
                equipment_list.append(equipment)

        return equipment_list

    def generate_maintenance(self, playground_ids: List[str], count_per_playground: Tuple[int, int] = (1, 3)) -> List[Dict[str, Any]]:
        """
        Génère des maintenances pour les terrains.

        Args:
            playground_ids: Liste des IDs de terrains
            count_per_playground: Tuple (min, max) de maintenances par terrain

        Returns:
            Liste des données de maintenances
        """
        maintenance_types = ['Préventive', 'Corrective', 'Urgente', 'Saisonnière', 'Rénovation']
        statuses = ['Planifiée', 'En cours', 'Terminée', 'Reportée', 'Annulée']
        technicians = [
            'Jean Jardinier', 'Marie Réparatrice', 'Pierre Nettoyeur',
            'Sophie Maintenance', 'Thomas Technicien', 'Équipe Maintenance'
        ]

        maintenance_list = []
        today = datetime.now().date()

        for playground_id in playground_ids:
            maintenance_count = random.randint(*count_per_playground)

            for _ in range(maintenance_count):
                maintenance_type = random.choice(maintenance_types)

                # Générer des dates réalistes
                # 40% passées, 20% en cours/proche, 40% futures
                rand = random.random()
                if rand < 0.4:  # Passées
                    scheduled_date = self.fake.date_between(start_date='-6m', end_date='-1d')
                    status = random.choice(['Terminée', 'Annulée'])
                    completed_date = scheduled_date + timedelta(days=random.randint(0, 3)) if status == 'Terminée' else None
                elif rand < 0.6:  # En cours/proche
                    scheduled_date = self.fake.date_between(start_date='-7d', end_date='+7d')
                    status = random.choice(['Planifiée', 'En cours'])
                    completed_date = None
                else:  # Futures
                    scheduled_date = self.fake.date_between(start_date='+1d', end_date='+6m')
                    status = 'Planifiée'
                    completed_date = None

                # Générer description selon le type
                descriptions = {
                    'Préventive': [
                        'Entretien mensuel du terrain et vérification des équipements',
                        'Contrôle de sécurité et maintenance préventive',
                        'Nettoyage approfondi et vérification des installations'
                    ],
                    'Corrective': [
                        'Réparation des équipements défaillants',
                        'Correction des défauts constatés lors de l\'inspection',
                        'Remplacement des éléments usagés'
                    ],
                    'Urgente': [
                        'Intervention d\'urgence suite à un incident',
                        'Réparation urgente pour sécurité',
                        'Remise en état d\'urgence'
                    ],
                    'Saisonnière': [
                        'Préparation hivernale du terrain',
                        'Maintenance de début de saison',
                        'Adaptation saisonnière des équipements'
                    ],
                    'Rénovation': [
                        'Rénovation complète du terrain',
                        'Modernisation des installations',
                        'Mise aux normes et amélioration'
                    ]
                }

                description = random.choice(descriptions[maintenance_type])

                # Coût selon le type et statut
                cost_ranges = {
                    'Préventive': (50, 200),
                    'Corrective': (100, 500),
                    'Urgente': (200, 800),
                    'Saisonnière': (300, 1000),
                    'Rénovation': (1000, 5000)
                }

                cost = None
                if status == 'Terminée':
                    cost = round(random.uniform(*cost_ranges[maintenance_type]), 2)
                elif status in ['En cours', 'Planifiée'] and random.random() < 0.5:
                    # Estimation pour certaines maintenances planifiées
                    cost = round(random.uniform(*cost_ranges[maintenance_type]), 2)

                maintenance = {
                    'Playground__c': playground_id,
                    'Maintenance_Type__c': maintenance_type,
                    'Scheduled_Date__c': scheduled_date.isoformat(),
                    'Completed_Date__c': completed_date.isoformat() if completed_date else None,
                    'Status__c': status,
                    'Description__c': description,
                    'Cost__c': cost,
                    'Technician__c': random.choice(technicians),
                    'Impact_on_Reservations__c': random.choice([True, False])
                }

                maintenance_list.append(maintenance)

        return maintenance_list

    def generate_reservations(
        self,
        contact_ids: List[str],
        playground_ids: List[str],
        count: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Génère des réservations réalistes.

        Args:
            contact_ids: Liste des IDs de contacts
            playground_ids: Liste des IDs de terrains
            count: Nombre total de réservations à générer

        Returns:
            Liste des données de réservations
        """
        if not contact_ids or not playground_ids:
            raise ValueError("Les IDs de contacts et terrains sont requis")

        statuses = ['En attente', 'Confirmée', 'En cours', 'Terminée', 'Annulée', 'No-show']
        payment_statuses = ['En attente', 'Payé', 'Partiellement payé', 'Remboursé', 'Annulé']

        reservations = []
        today = datetime.now()

        for _ in range(count):
            contact_id = random.choice(contact_ids)
            playground_id = random.choice(playground_ids)

            # Générer des dates réalistes
            # 30% passées, 20% présentes/proches, 50% futures
            rand = random.random()
            if rand < 0.3:  # Passées
                reservation_date = self.fake.date_between(start_date='-3m', end_date='-1d')
                status = random.choice(['Terminée', 'Annulée', 'No-show'])
                payment_status = 'Payé' if status == 'Terminée' else random.choice(['Remboursé', 'Annulé'])
            elif rand < 0.5:  # Présentes/proches
                reservation_date = self.fake.date_between(start_date='-3d', end_date='+3d')
                status = random.choice(['Confirmée', 'En cours'])
                payment_status = random.choice(['Payé', 'En attente'])
            else:  # Futures
                reservation_date = self.fake.date_between(start_date='+1d', end_date='+2m')
                status = random.choice(['En attente', 'Confirmée'])
                payment_status = random.choice(['En attente', 'Payé'])

            # Générer des heures réalistes (8h-22h)
            start_hour = random.randint(8, 20)
            start_minute = random.choice([0, 30])
            duration_hours = random.choice([1.0, 1.5, 2.0, 2.5, 3.0])

            start_time = datetime.combine(reservation_date, datetime.min.time()).replace(
                hour=start_hour, minute=start_minute
            )
            end_time = start_time + timedelta(hours=duration_hours)

            # Générer tarif réaliste selon la durée
            hourly_rate = random.uniform(20, 60)
            total_amount = round(hourly_rate * duration_hours, 2)

            # Nombre de joueurs selon le type de terrain (simulé)
            number_of_players = random.randint(2, 20)

            # Demandes spéciales (optionnelles)
            special_requests_options = [
                None,
                'Match amical entre équipes locales',
                'Cours particulier',
                'Entraînement équipe junior',
                'Tournoi inter-entreprises',
                'Événement d\'entreprise',
                'Fête d\'anniversaire',
                'Stage de formation',
                'Compétition officielle'
            ]

            special_requests = random.choice(special_requests_options)

            # Raison d'annulation si applicable
            cancellation_reason = None
            if status in ['Annulée', 'No-show']:
                cancellation_reasons = [
                    'Météo défavorable',
                    'Maladie du réservant',
                    'Problème technique sur le terrain',
                    'Changement de programme',
                    'Annulation de dernière minute',
                    'Pas de présentation (no-show)'
                ]
                cancellation_reason = random.choice(cancellation_reasons)

            # Notes internes (optionnelles)
            notes = None
            if random.random() < 0.3:  # 30% ont des notes
                notes_options = [
                    'Client régulier, très ponctuel',
                    'Première réservation, bien accueillir',
                    'Groupe nombreux, vérifier capacité',
                    'Demande particulière pour l\'éclairage',
                    'Client VIP, service premium',
                    'Attention aux horaires de fin'
                ]
                notes = random.choice(notes_options)

            reservation = {
                'Contact__c': contact_id,
                'Playground__c': playground_id,
                'Reservation_Date__c': reservation_date.isoformat(),
                'Start_Time__c': start_time.isoformat(),
                'End_Time__c': end_time.isoformat(),
                'Duration_Hours__c': duration_hours,
                'Status__c': status,
                'Total_Amount__c': total_amount,
                'Payment_Status__c': payment_status,
                'Number_of_Players__c': number_of_players,
                'Special_Requests__c': special_requests,
                'Cancellation_Reason__c': cancellation_reason,
                'Notes__c': notes
            }

            reservations.append(reservation)

        return reservations

    def generate_complete_dataset(
        self,
        accounts_count: int = 5,
        contacts_count: int = 15,
        playgrounds_count: int = 8,
        reservations_count: int = 50
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Génère un jeu de données complet et cohérent.

        Args:
            accounts_count: Nombre de comptes à générer
            contacts_count: Nombre de contacts à générer
            playgrounds_count: Nombre de terrains à générer
            reservations_count: Nombre de réservations à générer

        Returns:
            Dictionnaire contenant toutes les données générées
        """
        logger.info("Génération d'un jeu de données complet")

        # Générer les données dans l'ordre des dépendances
        accounts = self.generate_accounts(accounts_count)
        logger.info(f"Généré {len(accounts)} comptes")

        # Pour la génération, nous simulons les IDs
        account_ids = [f"001{str(i).zfill(15)}" for i in range(len(accounts))]

        contacts = self.generate_contacts(contacts_count, account_ids)
        logger.info(f"Généré {len(contacts)} contacts")

        contact_ids = [f"003{str(i).zfill(15)}" for i in range(len(contacts))]

        playgrounds = self.generate_playgrounds(playgrounds_count, account_ids)
        logger.info(f"Généré {len(playgrounds)} terrains")

        playground_ids = [f"a00{str(i).zfill(15)}" for i in range(len(playgrounds))]

        equipment = self.generate_equipment(playground_ids)
        logger.info(f"Généré {len(equipment)} équipements")

        maintenance = self.generate_maintenance(playground_ids)
        logger.info(f"Généré {len(maintenance)} maintenances")

        reservations = self.generate_reservations(contact_ids, playground_ids, reservations_count)
        logger.info(f"Généré {len(reservations)} réservations")

        return {
            'accounts': accounts,
            'contacts': contacts,
            'playgrounds': playgrounds,
            'equipment': equipment,
            'maintenance': maintenance,
            'reservations': reservations
        }
