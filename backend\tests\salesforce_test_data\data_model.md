# Modèle de Données - Système de Réservation de Terrain de Jeux

## Vue d'ensemble

Ce modèle utilise les objets standard Salesforce (Account, Contact) et des objets custom pour créer un système complet de réservation de terrains de jeux.

## Objets Standard Utilisés

### Account (Compte)
- **Utilisation** : Représente les organisations/clubs qui gèrent les terrains
- **Champs utilisés** :
  - Name : Nom du club/organisation
  - Type : Type d'organisation (Club Sportif, Municipalité, Privé)
  - Phone : Téléphone de contact
  - Website : Site web
  - BillingAddress : Adresse de facturation
  - Description : Description de l'organisation

### Contact
- **Utilisation** : Représente les utilisateurs qui font des réservations
- **Champs utilisés** :
  - FirstName, LastName : Nom et prénom
  - Email : Email de contact
  - Phone : Téléphone
  - AccountId : Lien vers l'organisation (optionnel)
  - MailingAddress : Adresse postale
  - Birthdate : Date de naissance
  - Description : Notes sur le contact

## Objets Custom à Créer

### Playground__c (Terrain de Jeux)
- **Description** : Représente un terrain de jeux spécifique
- **Champs** :
  - Name : Nom du terrain (ex: "Terrain A", "Court Central")
  - Account__c (Lookup to Account) : Organisation propriétaire
  - Playground_Type__c (Picklist) : Type de terrain (Football, Tennis, Basketball, Multi-sport)
  - Surface_Type__c (Picklist) : Type de surface (Gazon, Synthétique, Béton, Terre battue)
  - Capacity__c (Number) : Capacité maximale de joueurs
  - Hourly_Rate__c (Currency) : Tarif horaire
  - Is_Active__c (Checkbox) : Terrain actif/inactif
  - Equipment_Available__c (Multi-select Picklist) : Équipements disponibles
  - Location__c (Geolocation) : Coordonnées GPS
  - Description__c (Long Text Area) : Description détaillée
  - Opening_Hours__c (Text) : Heures d'ouverture
  - Special_Rules__c (Long Text Area) : Règles spéciales

### Reservation__c (Réservation)
- **Description** : Représente une réservation de terrain
- **Champs** :
  - Name : Numéro de réservation (auto-généré)
  - Contact__c (Lookup to Contact) : Personne qui réserve
  - Playground__c (Lookup to Playground__c) : Terrain réservé
  - Reservation_Date__c (Date) : Date de la réservation
  - Start_Time__c (DateTime) : Heure de début
  - End_Time__c (DateTime) : Heure de fin
  - Duration_Hours__c (Number) : Durée en heures (calculé)
  - Status__c (Picklist) : Statut (Confirmée, En attente, Annulée, Terminée)
  - Total_Amount__c (Currency) : Montant total (calculé)
  - Payment_Status__c (Picklist) : Statut paiement (En attente, Payé, Remboursé)
  - Number_of_Players__c (Number) : Nombre de joueurs prévus
  - Special_Requests__c (Long Text Area) : Demandes spéciales
  - Cancellation_Reason__c (Text) : Raison d'annulation
  - Created_By_Contact__c (Lookup to Contact) : Contact créateur
  - Notes__c (Long Text Area) : Notes internes

### Equipment__c (Équipement)
- **Description** : Équipements disponibles pour les terrains
- **Champs** :
  - Name : Nom de l'équipement
  - Equipment_Type__c (Picklist) : Type (Ballon, Filet, Raquette, Éclairage, etc.)
  - Playground__c (Lookup to Playground__c) : Terrain associé
  - Quantity_Available__c (Number) : Quantité disponible
  - Condition__c (Picklist) : État (Excellent, Bon, Acceptable, À remplacer)
  - Purchase_Date__c (Date) : Date d'achat
  - Last_Maintenance__c (Date) : Dernière maintenance
  - Is_Available__c (Checkbox) : Disponible ou non
  - Rental_Rate__c (Currency) : Tarif de location (optionnel)

### Maintenance__c (Maintenance)
- **Description** : Suivi de la maintenance des terrains
- **Champs** :
  - Name : Référence de maintenance
  - Playground__c (Lookup to Playground__c) : Terrain concerné
  - Maintenance_Type__c (Picklist) : Type (Préventive, Corrective, Urgente)
  - Scheduled_Date__c (Date) : Date prévue
  - Completed_Date__c (Date) : Date de réalisation
  - Status__c (Picklist) : Statut (Planifiée, En cours, Terminée, Reportée)
  - Description__c (Long Text Area) : Description des travaux
  - Cost__c (Currency) : Coût des travaux
  - Technician__c (Text) : Nom du technicien
  - Impact_on_Reservations__c (Checkbox) : Impact sur les réservations

## Relations entre Objets

```
Account (1) -----> (N) Playground__c
Contact (1) -----> (N) Reservation__c
Playground__c (1) -----> (N) Reservation__c
Playground__c (1) -----> (N) Equipment__c
Playground__c (1) -----> (N) Maintenance__c
Contact (0..1) -----> (N) Account (relation optionnelle)
```

## Données de Test à Créer

### Comptes (3-5 organisations)
1. **Club Sportif Municipal** - Municipalité
2. **Tennis Club Elite** - Club privé
3. **Centre Sportif Universitaire** - Institution
4. **Association Quartier Sud** - Association
5. **Complexe Sportif Privé** - Entreprise privée

### Contacts (15-20 utilisateurs)
- Mix de contacts liés et non liés aux comptes
- Différents profils d'âge et d'utilisation
- Emails et téléphones variés

### Terrains (8-12 terrains)
- Différents types : Football, Tennis, Basketball, Multi-sport
- Différentes surfaces et capacités
- Répartis entre les différentes organisations

### Réservations (50-100 réservations)
- Réservations passées, présentes et futures
- Différents statuts et montants
- Répartition réaliste sur les terrains et contacts

### Équipements (20-30 équipements)
- Variété d'équipements par terrain
- Différents états et quantités

### Maintenances (10-15 maintenances)
- Mix de maintenances planifiées et terminées
- Différents types et coûts

## Validation du Modèle Générique

Ce modèle permettra de tester :

1. **Extraction d'objets standard** (Account, Contact)
2. **Extraction d'objets custom** avec différents types de champs
3. **Relations Lookup** entre objets
4. **Champs calculés** et formules
5. **Listes de sélection** (Picklist)
6. **Champs de géolocalisation**
7. **Champs de devise** et calculs
8. **Champs de date/heure** complexes
9. **Préservation des métadonnées** Salesforce
10. **Gestion des permissions** et ownership
