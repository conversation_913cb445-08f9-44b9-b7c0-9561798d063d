# Application Configuration
APP_NAME=FungAI Backend
APP_VERSION=0.1.0
DEBUG=true
LOG_LEVEL=INFO

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/fungai

# Salesforce Configuration
SALESFORCE_USERNAME=
SALESFORCE_PASSWORD=
SALESFORCE_SECURITY_TOKEN=
SALESFORCE_DOMAIN=login  # or test for sandbox
SALESFORCE_API_VERSION=58.0

# Salesforce OAuth Configuration (optionnel)
SALESFORCE_CONSUMER_KEY=
SALESFORCE_CONSUMER_SECRET=
SALESFORCE_AUTH_TYPE=password  # 'password' ou 'oauth'

# Alternative avec préfixe SF_ (plus court)
SF_USERNAME=
SF_PASSWORD=
SF_SECURITY_TOKEN=
SF_CONSUMER_KEY=
SF_CONSUMER_SECRET=
SF_AUTH_TYPE=password
SF_DOMAIN=login
SF_API_VERSION=58.0

# Oracle Database Configuration
ORACLE_HOST=
ORACLE_PORT=1521
ORACLE_SERVICE_NAME=
ORACLE_USERNAME=
ORACLE_PASSWORD=

# SQL Server Configuration
SQLSERVER_HOST=
SQLSERVER_PORT=1433
SQLSERVER_DATABASE=
SQLSERVER_USERNAME=
SQLSERVER_PASSWORD=
SQLSERVER_DRIVER=ODBC Driver 17 for SQL Server

# Vector Database Configuration
VECTOR_DB_TYPE=chromadb  # chromadb or qdrant
CHROMADB_PATH=./data/chromadb
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=

# LLM Configuration
LLM_MODEL_NAME=microsoft/DialoGPT-medium
LLM_MODEL_PATH=./models
LLM_DEVICE=cpu  # cpu or cuda
LLM_MAX_LENGTH=512
EMBEDDING_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2

# Processing Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_CONCURRENT_EXTRACTIONS=5
BATCH_SIZE=1000

# Security Configuration
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
