#!/usr/bin/env python3
"""
Script de test rapide pour valider l'authentification Salesforce.

Ce script teste les deux méthodes d'authentification :
1. OAuth 2.0 (recommandé)
2. Username/Password/Security Token (fallback)
"""

import os
import sys
import asyncio
from pathlib import Path
from loguru import logger

# Ajouter le répertoire src au path
sys.path.append(str(Path(__file__).parent / "src"))

from src.core.salesforce_auth import create_authenticator_from_env
from src.core.models import DataSource, SourceType
from src.extractors.salesforce_extractor import SalesforceExtractor


async def test_authentication():
    """Test l'authentification Salesforce avec les deux méthodes."""
    
    logger.info("🔐 Test d'authentification Salesforce")
    logger.info("=" * 50)
    
    # Vérifier les variables d'environnement
    required_vars = ['SF_USERNAME', 'SF_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Variables d'environnement manquantes: {missing_vars}")
        logger.info("💡 Assurez-vous que le fichier .env est configuré correctement")
        return False
    
    # Afficher la configuration (sans les secrets)
    logger.info(f"👤 Utilisateur: {os.getenv('SF_USERNAME')}")
    logger.info(f"🌐 Domaine: {os.getenv('SF_DOMAIN', 'login')}")
    logger.info(f"🔧 Type d'auth: {os.getenv('SF_AUTH_TYPE', 'password')}")
    logger.info(f"🔑 Consumer Key: {'✓' if os.getenv('SF_CONSUMER_KEY') else '✗'}")
    logger.info(f"🔐 Consumer Secret: {'✓' if os.getenv('SF_CONSUMER_SECRET') else '✗'}")
    logger.info("")
    
    # Test 1: Authentification directe
    logger.info("🧪 Test 1: Authentification directe")
    try:
        authenticator = create_authenticator_from_env()
        
        # Test de connexion
        if authenticator.test_connection():
            logger.info("✅ Authentification directe réussie")
            
            # Obtenir les informations de connexion
            sf = authenticator.authenticate()
            
            # Test de requête simple
            result = sf.query("SELECT Id, Name, Username FROM User LIMIT 1")
            if result['records']:
                user = result['records'][0]
                logger.info(f"👤 Utilisateur connecté: {user.get('Name', 'Inconnu')} ({user.get('Username', 'N/A')})")
            
            # Test de requête sur les objets
            objects_result = sf.query("SELECT COUNT() FROM Account")
            logger.info(f"📊 Nombre de comptes dans l'org: {objects_result['totalSize']}")
            
        else:
            logger.error("❌ Authentification directe échouée")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'authentification directe: {str(e)}")
        return False
    
    logger.info("")
    
    # Test 2: Authentification via l'extracteur
    logger.info("🧪 Test 2: Authentification via l'extracteur Salesforce")
    try:
        # Créer la configuration de source de données
        config = {
            'username': os.getenv('SF_USERNAME'),
            'password': os.getenv('SF_PASSWORD'),
            'security_token': os.getenv('SF_SECURITY_TOKEN', ''),
            'consumer_key': os.getenv('SF_CONSUMER_KEY'),
            'consumer_secret': os.getenv('SF_CONSUMER_SECRET'),
            'auth_type': os.getenv('SF_AUTH_TYPE', 'password'),
            'domain': os.getenv('SF_DOMAIN', 'login'),
            'api_version': os.getenv('SF_API_VERSION', '58.0')
        }
        
        data_source = DataSource(
            name="Test Salesforce Org",
            source_type=SourceType.SALESFORCE,
            connection_config=config
        )
        
        # Créer l'extracteur
        extractor = SalesforceExtractor(data_source)
        
        # Test de connexion
        if await extractor.test_connection():
            logger.info("✅ Authentification extracteur réussie")
            
            # Test de découverte de schéma (rapide)
            logger.info("🔍 Test de découverte de schéma...")
            
            async with extractor:
                # Découvrir quelques objets standard
                account_schema = await extractor.get_entity_schema('Account')
                if account_schema:
                    logger.info(f"📋 Schéma Account: {len(account_schema.fields)} champs")
                
                contact_schema = await extractor.get_entity_schema('Contact')
                if contact_schema:
                    logger.info(f"📋 Schéma Contact: {len(contact_schema.fields)} champs")
                
                # Test de comptage d'enregistrements
                account_count = await extractor.get_record_count('Account')
                logger.info(f"📊 Nombre de comptes via extracteur: {account_count}")
                
        else:
            logger.error("❌ Authentification extracteur échouée")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erreur lors du test extracteur: {str(e)}")
        return False
    
    logger.info("")
    logger.info("🎉 Tous les tests d'authentification ont réussi !")
    logger.info("✅ Votre configuration Salesforce est correcte")
    logger.info("🚀 Vous pouvez maintenant exécuter les tests complets")
    
    return True


async def test_oauth_vs_password():
    """Compare les performances OAuth vs Password."""
    
    logger.info("⚡ Comparaison OAuth vs Password")
    logger.info("=" * 40)
    
    import time
    
    # Test OAuth
    if os.getenv('SF_CONSUMER_KEY') and os.getenv('SF_CONSUMER_SECRET'):
        logger.info("🧪 Test OAuth...")
        
        # Forcer OAuth
        os.environ['SF_AUTH_TYPE'] = 'oauth'
        
        start_time = time.time()
        try:
            authenticator = create_authenticator_from_env()
            sf = authenticator.authenticate()
            sf.query("SELECT COUNT() FROM User LIMIT 1")
            oauth_time = time.time() - start_time
            logger.info(f"✅ OAuth: {oauth_time:.2f}s")
        except Exception as e:
            logger.error(f"❌ OAuth échoué: {str(e)}")
            oauth_time = None
    else:
        logger.warning("⚠️  OAuth non configuré (Consumer Key/Secret manquants)")
        oauth_time = None
    
    # Test Password
    logger.info("🧪 Test Password...")
    
    # Forcer Password
    os.environ['SF_AUTH_TYPE'] = 'password'
    
    start_time = time.time()
    try:
        authenticator = create_authenticator_from_env()
        sf = authenticator.authenticate()
        sf.query("SELECT COUNT() FROM User LIMIT 1")
        password_time = time.time() - start_time
        logger.info(f"✅ Password: {password_time:.2f}s")
    except Exception as e:
        logger.error(f"❌ Password échoué: {str(e)}")
        password_time = None
    
    # Comparaison
    if oauth_time and password_time:
        if oauth_time < password_time:
            logger.info(f"🏆 OAuth est plus rapide de {password_time - oauth_time:.2f}s")
        else:
            logger.info(f"🏆 Password est plus rapide de {oauth_time - password_time:.2f}s")
    
    # Restaurer la configuration originale
    original_auth_type = 'oauth' if os.getenv('SF_CONSUMER_KEY') else 'password'
    os.environ['SF_AUTH_TYPE'] = original_auth_type


def main():
    """Point d'entrée principal."""
    
    # Configuration du logging
    logger.remove()
    logger.add(
        sys.stdout,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    logger.info("🚀 Test d'authentification Salesforce FungAI")
    logger.info("=" * 60)
    
    try:
        # Test principal
        success = asyncio.run(test_authentication())
        
        if success:
            # Test de comparaison (optionnel)
            logger.info("")
            asyncio.run(test_oauth_vs_password())
            
            logger.info("")
            logger.info("🎯 Prochaines étapes:")
            logger.info("   1. Exécuter les tests complets: python tests/salesforce_test_data/run_salesforce_tests.py")
            logger.info("   2. Ou tester avec pytest: pytest tests/test_salesforce_integration.py -v")
            
        else:
            logger.error("❌ Tests d'authentification échoués")
            logger.info("💡 Vérifiez votre configuration dans backend/.env")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("⏹️  Test interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Erreur inattendue: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
