"""
Base classes for data extractors.

This module defines the abstract base classes that all extractors must implement
to ensure consistent behavior across different data sources.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Iterator, AsyncIterator
from uuid import UUID
import asyncio
from loguru import logger

from .models import (
    DataSource, EntitySchema, GenericRecord, ExtractionBatch,
    SourceType, FieldMetadata, RelationshipMetadata
)


class ExtractorError(Exception):
    """Base exception for extractor errors."""
    pass


class ConnectionError(ExtractorError):
    """Exception raised when connection to data source fails."""
    pass


class SchemaDiscoveryError(ExtractorError):
    """Exception raised during schema discovery."""
    pass


class DataExtractionError(ExtractorError):
    """Exception raised during data extraction."""
    pass


class BaseExtractor(ABC):
    """
    Abstract base class for all data extractors.
    
    This class defines the interface that all extractors must implement
    to work with the generic data model.
    """
    
    def __init__(self, source: DataSource):
        """Initialize the extractor with a data source configuration."""
        self.source = source
        self.is_connected = False
        self._connection = None
    
    @property
    @abstractmethod
    def source_type(self) -> SourceType:
        """Return the source type this extractor handles."""
        pass
    
    @abstractmethod
    async def connect(self) -> None:
        """Establish connection to the data source."""
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """Close connection to the data source."""
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """Test if connection to data source is working."""
        pass
    
    @abstractmethod
    async def discover_schema(self) -> Dict[str, EntitySchema]:
        """
        Discover and return the schema of all entities in the data source.
        
        Returns:
            Dictionary mapping entity names to their schemas.
        """
        pass
    
    @abstractmethod
    async def get_entity_schema(self, entity_name: str) -> Optional[EntitySchema]:
        """
        Get schema for a specific entity.
        
        Args:
            entity_name: Name of the entity
            
        Returns:
            EntitySchema if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def extract_entity_data(
        self, 
        entity_name: str, 
        batch_size: int = 1000,
        filters: Optional[Dict[str, Any]] = None
    ) -> AsyncIterator[List[GenericRecord]]:
        """
        Extract data from a specific entity.
        
        Args:
            entity_name: Name of the entity to extract
            batch_size: Number of records per batch
            filters: Optional filters to apply
            
        Yields:
            Batches of GenericRecord objects
        """
        pass
    
    @abstractmethod
    async def get_record_count(
        self, 
        entity_name: str,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        Get total number of records in an entity.
        
        Args:
            entity_name: Name of the entity
            filters: Optional filters to apply
            
        Returns:
            Total record count
        """
        pass
    
    async def extract_all_data(
        self, 
        batch_size: int = 1000,
        entity_filter: Optional[List[str]] = None
    ) -> AsyncIterator[GenericRecord]:
        """
        Extract all data from all entities in the source.
        
        Args:
            batch_size: Number of records per batch
            entity_filter: Optional list of entity names to extract
            
        Yields:
            Individual GenericRecord objects
        """
        try:
            # Discover schema first
            schemas = await self.discover_schema()
            
            # Filter entities if specified
            entities_to_extract = entity_filter or list(schemas.keys())
            
            # Remove excluded entities
            entities_to_extract = [
                entity for entity in entities_to_extract 
                if entity not in self.source.excluded_entities
            ]
            
            logger.info(f"Extracting data from {len(entities_to_extract)} entities")
            
            for entity_name in entities_to_extract:
                logger.info(f"Starting extraction for entity: {entity_name}")
                
                try:
                    async for batch in self.extract_entity_data(entity_name, batch_size):
                        for record in batch:
                            # Apply field exclusions
                            self._apply_field_exclusions(record)
                            yield record
                            
                except Exception as e:
                    logger.error(f"Error extracting entity {entity_name}: {str(e)}")
                    raise DataExtractionError(f"Failed to extract {entity_name}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error during full extraction: {str(e)}")
            raise
    
    def _apply_field_exclusions(self, record: GenericRecord) -> None:
        """Apply field exclusions to a record."""
        excluded_fields = self.source.excluded_fields.get(record.entity_name, set())
        
        for field_name in excluded_fields:
            if field_name in record.data:
                del record.data[field_name]
    
    async def validate_configuration(self) -> List[str]:
        """
        Validate the extractor configuration.
        
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check required connection parameters
        required_params = self.get_required_connection_params()
        for param in required_params:
            if param not in self.source.connection_config:
                errors.append(f"Missing required connection parameter: {param}")
        
        # Test connection if configuration seems valid
        if not errors:
            try:
                if not await self.test_connection():
                    errors.append("Connection test failed")
            except Exception as e:
                errors.append(f"Connection test error: {str(e)}")
        
        return errors
    
    @abstractmethod
    def get_required_connection_params(self) -> List[str]:
        """
        Get list of required connection parameters for this extractor.
        
        Returns:
            List of required parameter names
        """
        pass
    
    def create_extraction_batch(self) -> ExtractionBatch:
        """Create a new extraction batch for tracking."""
        return ExtractionBatch(
            source_id=self.source.id,
            source_name=self.source.name,
            extraction_config=self.source.extraction_config.copy()
        )
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()


class DatabaseExtractor(BaseExtractor):
    """
    Base class for database extractors (Oracle, SQL Server, etc.).
    
    Provides common functionality for relational database sources.
    """
    
    @abstractmethod
    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Execute a SQL query and return results.
        
        Args:
            query: SQL query to execute
            params: Optional query parameters
            
        Returns:
            List of result rows as dictionaries
        """
        pass
    
    @abstractmethod
    def build_select_query(
        self, 
        table_name: str, 
        columns: Optional[List[str]] = None,
        where_clause: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> str:
        """
        Build a SELECT query for the specific database dialect.
        
        Args:
            table_name: Name of the table
            columns: Columns to select (None for all)
            where_clause: Optional WHERE clause
            limit: Optional LIMIT
            offset: Optional OFFSET
            
        Returns:
            SQL query string
        """
        pass
    
    async def get_table_names(self) -> List[str]:
        """Get list of all table names in the database."""
        query = self.get_table_list_query()
        results = await self.execute_query(query)
        return [row['table_name'] for row in results]
    
    @abstractmethod
    def get_table_list_query(self) -> str:
        """Get SQL query to list all tables."""
        pass
    
    @abstractmethod
    def get_column_info_query(self, table_name: str) -> str:
        """Get SQL query to get column information for a table."""
        pass


class APIExtractor(BaseExtractor):
    """
    Base class for API-based extractors (REST, GraphQL, etc.).
    
    Provides common functionality for API-based data sources.
    """
    
    @abstractmethod
    async def make_request(
        self, 
        endpoint: str, 
        method: str = "GET",
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Make an API request.
        
        Args:
            endpoint: API endpoint
            method: HTTP method
            params: Query parameters
            data: Request body data
            headers: Request headers
            
        Returns:
            Response data
        """
        pass
    
    @abstractmethod
    async def get_api_schema(self) -> Dict[str, Any]:
        """
        Get API schema/metadata.
        
        Returns:
            API schema information
        """
        pass
    
    async def handle_pagination(
        self, 
        endpoint: str, 
        page_size: int = 100
    ) -> AsyncIterator[Dict[str, Any]]:
        """
        Handle paginated API responses.
        
        Args:
            endpoint: API endpoint
            page_size: Number of records per page
            
        Yields:
            Individual records from paginated responses
        """
        # This is a generic implementation that subclasses can override
        page = 1
        has_more = True
        
        while has_more:
            params = {"page": page, "page_size": page_size}
            response = await self.make_request(endpoint, params=params)
            
            # Extract records (this will vary by API)
            records = response.get("data", response.get("records", []))
            
            for record in records:
                yield record
            
            # Check if there are more pages
            has_more = len(records) == page_size
            page += 1
