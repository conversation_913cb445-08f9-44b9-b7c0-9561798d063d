# FungAI - Generic AI Data Integration Platform

## Architecture Overview

FungAI is a generic, installable, and self-configurable AI product designed to work with multiple data sources including Salesforce, Oracle, SQL Server, and more.

### Key Components

1. **Generic Data Model**: Universal data representation that can handle any data source
2. **Multi-Source Extractors**: Pluggable extractors for different data sources
3. **Crunching Engine**: Processes generic data model into vectorized documents
4. **Vector Database**: Local storage with ChromaDB/Qdrant
5. **Open-Source LLM**: Local AI processing
6. **Hybrid Security**: Leverages source system permissions

### Architecture Flow

```
Data Sources → Extractors → Generic Model → Crunching → Vector DB → LLM → API
```

## Project Structure

```
backend/
├── src/
│   ├── core/                 # Core generic data model
│   ├── extractors/           # Data source extractors
│   ├── crunching/           # Document processing engine
│   ├── vectordb/            # Vector database integration
│   ├── llm/                 # LLM integration
│   └── api/                 # FastAPI REST endpoints
├── tests/
├── docker/
└── config/
```

## Getting Started

[To be completed as we build the components]
