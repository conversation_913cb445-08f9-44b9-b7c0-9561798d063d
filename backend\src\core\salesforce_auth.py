"""
Module d'authentification Salesforce avec support OAuth et Username/Password.

Ce module gère les différents types d'authentification Salesforce
et fournit une interface unifiée pour les extracteurs.
"""

import os
import requests
from typing import Dict, Any, Optional
from loguru import logger
from simple_salesforce import Salesforce
from simple_salesforce.exceptions import SalesforceAuthenticationFailed


class SalesforceAuthenticator:
    """
    Gestionnaire d'authentification Salesforce unifié.
    
    Supporte :
    - Authentification Username/Password/Security Token
    - Authentification OAuth 2.0 (Client Credentials Flow)
    - Authentification OAuth 2.0 (Authorization Code Flow)
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialise l'authenticateur avec la configuration.
        
        Args:
            config: Configuration d'authentification Salesforce
        """
        self.config = config
        self.auth_type = config.get('auth_type', 'password').lower()
        self.domain = config.get('domain', 'login')
        self.api_version = config.get('api_version', '58.0')
        
    def authenticate(self) -> Salesforce:
        """
        Authentifie et retourne une instance Salesforce connectée.
        
        Returns:
            Instance Salesforce authentifiée
            
        Raises:
            SalesforceAuthenticationFailed: Si l'authentification échoue
            ValueError: Si la configuration est invalide
        """
        if self.auth_type == 'password':
            return self._authenticate_password()
        elif self.auth_type == 'oauth':
            return self._authenticate_oauth()
        else:
            raise ValueError(f"Type d'authentification non supporté: {self.auth_type}")
    
    def _authenticate_password(self) -> Salesforce:
        """
        Authentification Username/Password/Security Token.
        
        Returns:
            Instance Salesforce authentifiée
        """
        required_fields = ['username', 'password']
        missing_fields = [field for field in required_fields if not self.config.get(field)]
        
        if missing_fields:
            raise ValueError(f"Champs manquants pour l'authentification password: {missing_fields}")
        
        try:
            logger.info(f"Authentification Salesforce (password): {self.config['username']} sur {self.domain}")
            
            sf = Salesforce(
                username=self.config['username'],
                password=self.config['password'],
                security_token=self.config.get('security_token', ''),
                domain=self.domain,
                version=self.api_version
            )
            
            # Test de la connexion
            user_info = sf.query("SELECT Id, Name, Username FROM User WHERE Id = '{}'".format(sf.sf_session.headers['Authorization'].split('/')[-1]))
            logger.info(f"Authentification réussie - Utilisateur: {user_info['records'][0]['Name'] if user_info['records'] else 'Inconnu'}")
            
            return sf
            
        except SalesforceAuthenticationFailed as e:
            logger.error(f"Échec de l'authentification Salesforce: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Erreur lors de l'authentification Salesforce: {str(e)}")
            raise SalesforceAuthenticationFailed(f"Erreur d'authentification: {str(e)}")
    
    def _authenticate_oauth(self) -> Salesforce:
        """
        Authentification OAuth 2.0 Client Credentials Flow.
        
        Returns:
            Instance Salesforce authentifiée
        """
        required_fields = ['consumer_key', 'consumer_secret', 'username', 'password']
        missing_fields = [field for field in required_fields if not self.config.get(field)]
        
        if missing_fields:
            raise ValueError(f"Champs manquants pour l'authentification OAuth: {missing_fields}")
        
        try:
            logger.info(f"Authentification Salesforce (OAuth): {self.config['username']} sur {self.domain}")
            
            # Obtenir le token OAuth
            oauth_token = self._get_oauth_token()
            
            # Créer l'instance Salesforce avec le token
            sf = Salesforce(
                instance_url=oauth_token['instance_url'],
                session_id=oauth_token['access_token'],
                version=self.api_version
            )
            
            # Test de la connexion
            user_info = sf.query("SELECT Id, Name, Username FROM User LIMIT 1")
            logger.info(f"Authentification OAuth réussie - Org: {oauth_token['instance_url']}")
            
            return sf
            
        except Exception as e:
            logger.error(f"Erreur lors de l'authentification OAuth: {str(e)}")
            raise SalesforceAuthenticationFailed(f"Erreur d'authentification OAuth: {str(e)}")
    
    def _get_oauth_token(self) -> Dict[str, str]:
        """
        Obtient un token OAuth via le Client Credentials Flow.
        
        Returns:
            Dictionnaire contenant access_token et instance_url
        """
        # URL du token endpoint
        token_url = f"https://{self.domain}.salesforce.com/services/oauth2/token"
        
        # Paramètres pour le Client Credentials Flow avec Username/Password
        data = {
            'grant_type': 'password',
            'client_id': self.config['consumer_key'],
            'client_secret': self.config['consumer_secret'],
            'username': self.config['username'],
            'password': self.config['password'] + self.config.get('security_token', '')
        }
        
        logger.debug(f"Demande de token OAuth à: {token_url}")
        
        try:
            response = requests.post(token_url, data=data, timeout=30)
            response.raise_for_status()
            
            token_data = response.json()
            
            if 'access_token' not in token_data:
                raise ValueError(f"Token OAuth non reçu: {token_data}")
            
            logger.debug("Token OAuth obtenu avec succès")
            
            return {
                'access_token': token_data['access_token'],
                'instance_url': token_data['instance_url'],
                'token_type': token_data.get('token_type', 'Bearer')
            }
            
        except requests.exceptions.RequestException as e:
            raise ValueError(f"Erreur lors de la demande de token OAuth: {str(e)}")
        except ValueError as e:
            raise ValueError(f"Erreur de parsing du token OAuth: {str(e)}")
    
    def test_connection(self) -> bool:
        """
        Teste la connexion Salesforce.
        
        Returns:
            True si la connexion fonctionne, False sinon
        """
        try:
            sf = self.authenticate()
            
            # Test simple avec une requête
            result = sf.query("SELECT COUNT() FROM User LIMIT 1")
            return result['totalSize'] >= 0
            
        except Exception as e:
            logger.error(f"Test de connexion échoué: {str(e)}")
            return False
    
    def get_connection_info(self) -> Dict[str, Any]:
        """
        Retourne les informations de connexion (sans credentials sensibles).
        
        Returns:
            Dictionnaire avec les informations de connexion
        """
        return {
            'auth_type': self.auth_type,
            'domain': self.domain,
            'api_version': self.api_version,
            'username': self.config.get('username', 'Non configuré'),
            'has_consumer_key': bool(self.config.get('consumer_key')),
            'has_consumer_secret': bool(self.config.get('consumer_secret'))
        }


def create_salesforce_authenticator(
    username: str,
    password: str,
    security_token: str = "",
    consumer_key: Optional[str] = None,
    consumer_secret: Optional[str] = None,
    auth_type: str = "password",
    domain: str = "login",
    api_version: str = "58.0"
) -> SalesforceAuthenticator:
    """
    Factory function pour créer un authenticateur Salesforce.
    
    Args:
        username: Nom d'utilisateur Salesforce
        password: Mot de passe Salesforce
        security_token: Token de sécurité Salesforce
        consumer_key: Clé consumer OAuth (optionnel)
        consumer_secret: Secret consumer OAuth (optionnel)
        auth_type: Type d'authentification ('password' ou 'oauth')
        domain: Domaine Salesforce ('login' ou 'test')
        api_version: Version de l'API Salesforce
        
    Returns:
        Instance de SalesforceAuthenticator configurée
    """
    config = {
        'username': username,
        'password': password,
        'security_token': security_token,
        'consumer_key': consumer_key,
        'consumer_secret': consumer_secret,
        'auth_type': auth_type,
        'domain': domain,
        'api_version': api_version
    }
    
    return SalesforceAuthenticator(config)


def create_authenticator_from_env() -> SalesforceAuthenticator:
    """
    Crée un authenticateur à partir des variables d'environnement.
    
    Variables d'environnement utilisées :
    - SALESFORCE_USERNAME ou SF_USERNAME
    - SALESFORCE_PASSWORD ou SF_PASSWORD
    - SALESFORCE_SECURITY_TOKEN ou SF_SECURITY_TOKEN
    - SALESFORCE_CONSUMER_KEY ou SF_CONSUMER_KEY
    - SALESFORCE_CONSUMER_SECRET ou SF_CONSUMER_SECRET
    - SALESFORCE_AUTH_TYPE ou SF_AUTH_TYPE
    - SALESFORCE_DOMAIN ou SF_DOMAIN
    - SALESFORCE_API_VERSION ou SF_API_VERSION
    
    Returns:
        Instance de SalesforceAuthenticator configurée
        
    Raises:
        ValueError: Si les variables d'environnement requises sont manquantes
    """
    # Fonction helper pour récupérer les variables d'environnement avec fallback
    def get_env_var(primary: str, secondary: str, default: Optional[str] = None) -> Optional[str]:
        return os.getenv(primary) or os.getenv(secondary) or default
    
    config = {
        'username': get_env_var('SALESFORCE_USERNAME', 'SF_USERNAME'),
        'password': get_env_var('SALESFORCE_PASSWORD', 'SF_PASSWORD'),
        'security_token': get_env_var('SALESFORCE_SECURITY_TOKEN', 'SF_SECURITY_TOKEN', ''),
        'consumer_key': get_env_var('SALESFORCE_CONSUMER_KEY', 'SF_CONSUMER_KEY'),
        'consumer_secret': get_env_var('SALESFORCE_CONSUMER_SECRET', 'SF_CONSUMER_SECRET'),
        'auth_type': get_env_var('SALESFORCE_AUTH_TYPE', 'SF_AUTH_TYPE', 'password'),
        'domain': get_env_var('SALESFORCE_DOMAIN', 'SF_DOMAIN', 'login'),
        'api_version': get_env_var('SALESFORCE_API_VERSION', 'SF_API_VERSION', '58.0')
    }
    
    # Validation des champs requis
    if not config['username'] or not config['password']:
        raise ValueError("SALESFORCE_USERNAME et SALESFORCE_PASSWORD sont requis")
    
    if config['auth_type'] == 'oauth':
        if not config['consumer_key'] or not config['consumer_secret']:
            raise ValueError("SALESFORCE_CONSUMER_KEY et SALESFORCE_CONSUMER_SECRET sont requis pour OAuth")
    
    return SalesforceAuthenticator(config)
