"""
Définitions des objets custom Salesforce pour le système de réservation.

Ce module contient les définitions des objets custom et leurs champs
pour créer un système complet de réservation de terrain de jeux.
"""

from typing import Dict, List, Any
from dataclasses import dataclass


@dataclass
class FieldDefinition:
    """Définition d'un champ Salesforce."""
    name: str
    type: str
    label: str
    length: int = None
    precision: int = None
    scale: int = None
    required: bool = False
    unique: bool = False
    external_id: bool = False
    default_value: str = None
    description: str = None
    help_text: str = None
    picklist_values: List[str] = None
    relationship_name: str = None
    reference_to: str = None


@dataclass
class CustomObjectDefinition:
    """Définition d'un objet custom Salesforce."""
    api_name: str
    label: str
    plural_label: str
    description: str
    name_field_label: str = "Name"
    name_field_type: str = "Text"
    fields: List[FieldDefinition] = None
    
    def __post_init__(self):
        if self.fields is None:
            self.fields = []


# Définition de l'objet Playground__c (Terrain de Jeux)
PLAYGROUND_OBJECT = CustomObjectDefinition(
    api_name="Playground__c",
    label="Terrain de Jeux",
    plural_label="Terrains de Jeux",
    description="Représente un terrain de jeux ou terrain sportif disponible à la réservation",
    name_field_label="Nom du Terrain",
    fields=[
        FieldDefinition(
            name="Account__c",
            type="Lookup",
            label="Organisation Propriétaire",
            required=True,
            reference_to="Account",
            relationship_name="Account",
            description="Organisation qui gère ce terrain"
        ),
        FieldDefinition(
            name="Playground_Type__c",
            type="Picklist",
            label="Type de Terrain",
            required=True,
            picklist_values=["Football", "Tennis", "Basketball", "Volleyball", "Multi-sport", "Pétanque", "Autre"],
            description="Type de sport principal pour ce terrain"
        ),
        FieldDefinition(
            name="Surface_Type__c",
            type="Picklist",
            label="Type de Surface",
            required=True,
            picklist_values=["Gazon naturel", "Gazon synthétique", "Béton", "Terre battue", "Parquet", "Sable", "Autre"],
            description="Type de surface du terrain"
        ),
        FieldDefinition(
            name="Capacity__c",
            type="Number",
            label="Capacité Maximale",
            precision=3,
            scale=0,
            required=True,
            description="Nombre maximum de joueurs simultanés"
        ),
        FieldDefinition(
            name="Hourly_Rate__c",
            type="Currency",
            label="Tarif Horaire",
            precision=8,
            scale=2,
            required=True,
            description="Tarif de location par heure en euros"
        ),
        FieldDefinition(
            name="Is_Active__c",
            type="Checkbox",
            label="Terrain Actif",
            default_value="true",
            description="Indique si le terrain est disponible à la réservation"
        ),
        FieldDefinition(
            name="Equipment_Available__c",
            type="MultiselectPicklist",
            label="Équipements Disponibles",
            picklist_values=[
                "Ballons", "Filets", "Raquettes", "Éclairage", "Vestiaires", 
                "Douches", "Parking", "Gradins", "Tableau de score", "Sonorisation"
            ],
            description="Équipements disponibles sur ce terrain"
        ),
        FieldDefinition(
            name="Location__c",
            type="Location",
            label="Localisation GPS",
            description="Coordonnées GPS du terrain"
        ),
        FieldDefinition(
            name="Description__c",
            type="LongTextArea",
            label="Description",
            length=32768,
            description="Description détaillée du terrain et de ses spécificités"
        ),
        FieldDefinition(
            name="Opening_Hours__c",
            type="Text",
            label="Heures d'Ouverture",
            length=255,
            description="Heures d'ouverture du terrain (ex: 8h-22h)"
        ),
        FieldDefinition(
            name="Special_Rules__c",
            type="LongTextArea",
            label="Règles Spéciales",
            length=32768,
            description="Règles spécifiques à ce terrain"
        )
    ]
)

# Définition de l'objet Reservation__c
RESERVATION_OBJECT = CustomObjectDefinition(
    api_name="Reservation__c",
    label="Réservation",
    plural_label="Réservations",
    description="Réservation d'un terrain de jeux par un contact",
    name_field_label="Numéro de Réservation",
    name_field_type="AutoNumber",
    fields=[
        FieldDefinition(
            name="Contact__c",
            type="Lookup",
            label="Contact Réservant",
            required=True,
            reference_to="Contact",
            relationship_name="Contact",
            description="Personne qui effectue la réservation"
        ),
        FieldDefinition(
            name="Playground__c",
            type="Lookup",
            label="Terrain Réservé",
            required=True,
            reference_to="Playground__c",
            relationship_name="Playground",
            description="Terrain faisant l'objet de la réservation"
        ),
        FieldDefinition(
            name="Reservation_Date__c",
            type="Date",
            label="Date de Réservation",
            required=True,
            description="Date pour laquelle le terrain est réservé"
        ),
        FieldDefinition(
            name="Start_Time__c",
            type="DateTime",
            label="Heure de Début",
            required=True,
            description="Heure de début de la réservation"
        ),
        FieldDefinition(
            name="End_Time__c",
            type="DateTime",
            label="Heure de Fin",
            required=True,
            description="Heure de fin de la réservation"
        ),
        FieldDefinition(
            name="Duration_Hours__c",
            type="Number",
            label="Durée (heures)",
            precision=4,
            scale=2,
            description="Durée de la réservation en heures (calculé automatiquement)"
        ),
        FieldDefinition(
            name="Status__c",
            type="Picklist",
            label="Statut",
            required=True,
            default_value="En attente",
            picklist_values=["En attente", "Confirmée", "En cours", "Terminée", "Annulée", "No-show"],
            description="Statut actuel de la réservation"
        ),
        FieldDefinition(
            name="Total_Amount__c",
            type="Currency",
            label="Montant Total",
            precision=10,
            scale=2,
            description="Montant total de la réservation (calculé automatiquement)"
        ),
        FieldDefinition(
            name="Payment_Status__c",
            type="Picklist",
            label="Statut Paiement",
            required=True,
            default_value="En attente",
            picklist_values=["En attente", "Payé", "Partiellement payé", "Remboursé", "Annulé"],
            description="Statut du paiement"
        ),
        FieldDefinition(
            name="Number_of_Players__c",
            type="Number",
            label="Nombre de Joueurs",
            precision=3,
            scale=0,
            description="Nombre de joueurs prévus"
        ),
        FieldDefinition(
            name="Special_Requests__c",
            type="LongTextArea",
            label="Demandes Spéciales",
            length=32768,
            description="Demandes ou besoins spéciaux pour cette réservation"
        ),
        FieldDefinition(
            name="Cancellation_Reason__c",
            type="Text",
            label="Raison d'Annulation",
            length=255,
            description="Raison de l'annulation si applicable"
        ),
        FieldDefinition(
            name="Notes__c",
            type="LongTextArea",
            label="Notes Internes",
            length=32768,
            description="Notes internes pour le personnel"
        )
    ]
)

# Définition de l'objet Equipment__c
EQUIPMENT_OBJECT = CustomObjectDefinition(
    api_name="Equipment__c",
    label="Équipement",
    plural_label="Équipements",
    description="Équipements disponibles pour les terrains de jeux",
    name_field_label="Nom de l'Équipement",
    fields=[
        FieldDefinition(
            name="Equipment_Type__c",
            type="Picklist",
            label="Type d'Équipement",
            required=True,
            picklist_values=[
                "Ballon Football", "Ballon Basketball", "Ballon Volleyball",
                "Raquette Tennis", "Raquette Badminton", "Filet Tennis", "Filet Volleyball",
                "Éclairage", "Tableau de Score", "Sifflet", "Plots", "Cônes", "Autre"
            ],
            description="Type d'équipement"
        ),
        FieldDefinition(
            name="Playground__c",
            type="Lookup",
            label="Terrain Associé",
            required=True,
            reference_to="Playground__c",
            relationship_name="Playground",
            description="Terrain auquel cet équipement est associé"
        ),
        FieldDefinition(
            name="Quantity_Available__c",
            type="Number",
            label="Quantité Disponible",
            precision=5,
            scale=0,
            required=True,
            default_value="1",
            description="Nombre d'unités disponibles"
        ),
        FieldDefinition(
            name="Condition__c",
            type="Picklist",
            label="État",
            required=True,
            default_value="Bon",
            picklist_values=["Excellent", "Bon", "Acceptable", "À remplacer", "Hors service"],
            description="État actuel de l'équipement"
        ),
        FieldDefinition(
            name="Purchase_Date__c",
            type="Date",
            label="Date d'Achat",
            description="Date d'achat de l'équipement"
        ),
        FieldDefinition(
            name="Last_Maintenance__c",
            type="Date",
            label="Dernière Maintenance",
            description="Date de la dernière maintenance"
        ),
        FieldDefinition(
            name="Is_Available__c",
            type="Checkbox",
            label="Disponible",
            default_value="true",
            description="Indique si l'équipement est disponible à l'utilisation"
        ),
        FieldDefinition(
            name="Rental_Rate__c",
            type="Currency",
            label="Tarif de Location",
            precision=8,
            scale=2,
            description="Tarif de location de l'équipement (optionnel)"
        )
    ]
)

# Définition de l'objet Maintenance__c
MAINTENANCE_OBJECT = CustomObjectDefinition(
    api_name="Maintenance__c",
    label="Maintenance",
    plural_label="Maintenances",
    description="Suivi de la maintenance des terrains de jeux",
    name_field_label="Référence Maintenance",
    name_field_type="AutoNumber",
    fields=[
        FieldDefinition(
            name="Playground__c",
            type="Lookup",
            label="Terrain Concerné",
            required=True,
            reference_to="Playground__c",
            relationship_name="Playground",
            description="Terrain faisant l'objet de la maintenance"
        ),
        FieldDefinition(
            name="Maintenance_Type__c",
            type="Picklist",
            label="Type de Maintenance",
            required=True,
            picklist_values=["Préventive", "Corrective", "Urgente", "Saisonnière", "Rénovation"],
            description="Type de maintenance à effectuer"
        ),
        FieldDefinition(
            name="Scheduled_Date__c",
            type="Date",
            label="Date Prévue",
            required=True,
            description="Date prévue pour la maintenance"
        ),
        FieldDefinition(
            name="Completed_Date__c",
            type="Date",
            label="Date de Réalisation",
            description="Date effective de réalisation de la maintenance"
        ),
        FieldDefinition(
            name="Status__c",
            type="Picklist",
            label="Statut",
            required=True,
            default_value="Planifiée",
            picklist_values=["Planifiée", "En cours", "Terminée", "Reportée", "Annulée"],
            description="Statut actuel de la maintenance"
        ),
        FieldDefinition(
            name="Description__c",
            type="LongTextArea",
            label="Description des Travaux",
            length=32768,
            required=True,
            description="Description détaillée des travaux de maintenance"
        ),
        FieldDefinition(
            name="Cost__c",
            type="Currency",
            label="Coût",
            precision=10,
            scale=2,
            description="Coût total de la maintenance"
        ),
        FieldDefinition(
            name="Technician__c",
            type="Text",
            label="Technicien",
            length=255,
            description="Nom du technicien responsable"
        ),
        FieldDefinition(
            name="Impact_on_Reservations__c",
            type="Checkbox",
            label="Impact sur Réservations",
            default_value="false",
            description="Indique si la maintenance impacte les réservations"
        )
    ]
)

# Liste de tous les objets custom à créer
CUSTOM_OBJECTS = [
    PLAYGROUND_OBJECT,
    RESERVATION_OBJECT,
    EQUIPMENT_OBJECT,
    MAINTENANCE_OBJECT
]


def get_object_creation_order() -> List[str]:
    """
    Retourne l'ordre de création des objets pour respecter les dépendances.
    
    Returns:
        Liste des noms d'API des objets dans l'ordre de création
    """
    return [
        "Playground__c",      # Dépend seulement d'Account (standard)
        "Equipment__c",       # Dépend de Playground__c
        "Maintenance__c",     # Dépend de Playground__c
        "Reservation__c"      # Dépend de Contact (standard) et Playground__c
    ]


def get_object_definition(api_name: str) -> CustomObjectDefinition:
    """
    Récupère la définition d'un objet custom par son nom d'API.
    
    Args:
        api_name: Nom d'API de l'objet (ex: "Playground__c")
        
    Returns:
        Définition de l'objet custom
        
    Raises:
        ValueError: Si l'objet n'est pas trouvé
    """
    for obj in CUSTOM_OBJECTS:
        if obj.api_name == api_name:
            return obj
    
    raise ValueError(f"Objet custom non trouvé: {api_name}")


def get_all_object_definitions() -> Dict[str, CustomObjectDefinition]:
    """
    Retourne toutes les définitions d'objets custom.
    
    Returns:
        Dictionnaire mapping nom d'API -> définition d'objet
    """
    return {obj.api_name: obj for obj in CUSTOM_OBJECTS}
