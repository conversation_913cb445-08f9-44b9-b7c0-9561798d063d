#!/bin/bash

# FungAI Backend Startup Script
# This script helps with development and deployment tasks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null; then
        print_error "docker-compose is not installed. Please install it and try again."
        exit 1
    fi
}

# Function to create .env file if it doesn't exist
setup_env() {
    if [ ! -f "backend/.env" ]; then
        print_status "Creating .env file from template..."
        cp backend/.env.example backend/.env
        print_warning "Please edit backend/.env with your configuration before running the application."
    fi
}

# Function to build and start services
start_services() {
    print_status "Building and starting FungAI services..."
    docker-compose up --build -d
    
    print_status "Waiting for services to be healthy..."
    sleep 10
    
    # Check service health
    if docker-compose ps | grep -q "unhealthy"; then
        print_error "Some services are unhealthy. Check logs with: docker-compose logs"
        exit 1
    fi
    
    print_success "All services are running!"
    print_status "Backend API: http://localhost:8000"
    print_status "API Documentation: http://localhost:8000/api/v1/docs"
    print_status "Qdrant Dashboard: http://localhost:6333/dashboard"
}

# Function to stop services
stop_services() {
    print_status "Stopping FungAI services..."
    docker-compose down
    print_success "Services stopped."
}

# Function to show logs
show_logs() {
    if [ -n "$1" ]; then
        docker-compose logs -f "$1"
    else
        docker-compose logs -f
    fi
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    docker-compose exec backend pytest
}

# Function to show help
show_help() {
    echo "FungAI Backend Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Build and start all services"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo "  logs      Show logs for all services"
    echo "  logs <service>  Show logs for specific service"
    echo "  test      Run tests"
    echo "  clean     Stop services and remove volumes"
    echo "  help      Show this help message"
    echo ""
    echo "Services: backend, postgres, qdrant, redis"
}

# Main script logic
case "${1:-start}" in
    start)
        check_docker
        check_docker_compose
        setup_env
        start_services
        ;;
    stop)
        check_docker
        check_docker_compose
        stop_services
        ;;
    restart)
        check_docker
        check_docker_compose
        stop_services
        start_services
        ;;
    logs)
        check_docker
        check_docker_compose
        show_logs "$2"
        ;;
    test)
        check_docker
        check_docker_compose
        run_tests
        ;;
    clean)
        check_docker
        check_docker_compose
        print_warning "This will remove all data volumes. Are you sure? (y/N)"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            docker-compose down -v
            print_success "Services stopped and volumes removed."
        else
            print_status "Operation cancelled."
        fi
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
