# Tests d'Intégration Salesforce

Ce module contient tous les outils nécessaires pour tester et valider l'extraction de données Salesforce avec notre modèle générique.

## Vue d'ensemble

Le système de test comprend :

1. **Modèle de données** : Système de réservation de terrain de jeux
2. **Génération de données** : Création de données de test réalistes
3. **Validation d'extraction** : Tests complets du processus d'extraction
4. **Nettoyage automatique** : Suppression des données de test

## Structure des Fichiers

```
salesforce_test_data/
├── README.md                    # Ce fichier
├── data_model.md               # Documentation du modèle de données
├── custom_objects.py           # Définitions des objets custom Salesforce
├── salesforce_test_manager.py  # Gestionnaire principal des tests
├── test_data_generator.py      # Générateur de données réalistes
├── run_salesforce_tests.py     # Script d'exécution des tests
└── .env.example                # Exemple de configuration
```

## Modèle de Données de Test

Le système utilise un modèle de **réservation de terrain de jeux** qui comprend :

### Objets Standard
- **Account** : Organisations gérant les terrains
- **Contact** : Utilisateurs effectuant les réservations

### Objets Custom
- **Playground__c** : Terrains de jeux/sport
- **Reservation__c** : Réservations de terrains
- **Equipment__c** : Équipements disponibles
- **Maintenance__c** : Suivi de la maintenance

### Relations
```
Account (1) -----> (N) Playground__c
Contact (1) -----> (N) Reservation__c
Playground__c (1) -----> (N) Reservation__c
Playground__c (1) -----> (N) Equipment__c
Playground__c (1) -----> (N) Maintenance__c
```

## Configuration

### 1. Variables d'Environnement

Créez un fichier `.env` dans le répertoire `backend/` avec :

```bash
# Configuration Salesforce
SF_USERNAME=<EMAIL>
SF_PASSWORD=votre_mot_de_passe
SF_SECURITY_TOKEN=votre_token_de_securite
SF_DOMAIN=test  # 'login' pour production, 'test' pour sandbox
SF_API_VERSION=58.0
```

### 2. Prérequis Salesforce

- **Org Salesforce** : Sandbox ou Developer Edition recommandé
- **Permissions** : Capacité à créer des objets custom (ou objets déjà créés)
- **API Access** : Accès aux APIs REST et Bulk

### 3. Objets Custom

Les objets custom doivent être créés dans votre org Salesforce. Vous pouvez :

1. **Créer manuellement** en suivant `data_model.md`
2. **Utiliser un package** (si disponible)
3. **Déployer via Metadata API** (pour les développeurs avancés)

## Utilisation

### Exécution Rapide

```bash
# Test complet (recommandé)
python tests/salesforce_test_data/run_salesforce_tests.py

# Test de découverte de schéma uniquement
python tests/salesforce_test_data/run_salesforce_tests.py --test-type schema

# Test d'extraction uniquement
python tests/salesforce_test_data/run_salesforce_tests.py --test-type extraction

# Mode debug avec logs détaillés
python tests/salesforce_test_data/run_salesforce_tests.py --log-level DEBUG
```

### Exécution avec pytest

```bash
# Tests d'intégration complets
pytest tests/test_salesforce_integration.py -m integration -v

# Tests spécifiques
pytest tests/test_salesforce_integration.py::TestSalesforceIntegration::test_complete_workflow -v
```

### Utilisation Programmatique

```python
from tests.salesforce_test_data.salesforce_test_manager import SalesforceTestManager
from tests.salesforce_test_data.test_data_generator import TestDataGenerator
from simple_salesforce import Salesforce

# Connexion Salesforce
sf = Salesforce(username='...', password='...', security_token='...')

# Gestionnaire de test
test_manager = SalesforceTestManager(sf)

# Configuration de l'environnement
await test_manager.setup_test_environment()

# Nettoyage
await test_manager.cleanup_test_data()
```

## Données de Test Générées

### Comptes (5)
- Club Sportif Municipal
- Tennis Club Elite  
- Centre Sportif Universitaire
- Association Quartier Sud
- Complexe Sportif Privé

### Contacts (15)
- Profils variés d'utilisateurs
- Associations optionnelles aux comptes
- Données personnelles réalistes

### Terrains (8)
- Types : Football, Tennis, Basketball, Multi-sport
- Surfaces variées : Gazon, Synthétique, Béton, etc.
- Tarifs et capacités réalistes

### Réservations (50)
- Répartition temporelle : passées, présentes, futures
- Statuts variés : Confirmée, Terminée, Annulée, etc.
- Montants calculés automatiquement

### Équipements (20-30)
- Ballons, raquettes, filets, éclairage
- États et quantités variables
- Tarifs de location optionnels

### Maintenances (10-15)
- Types : Préventive, Corrective, Urgente
- Planification réaliste
- Coûts et techniciens assignés

## Validation du Modèle Générique

Les tests valident :

### 1. Découverte de Schéma
- ✅ Extraction des métadonnées Salesforce
- ✅ Conversion vers le modèle générique
- ✅ Préservation des relations
- ✅ Types de champs corrects

### 2. Extraction de Données
- ✅ Utilisation du Bulk API 2.0
- ✅ Conversion vers GenericRecord
- ✅ Préservation des métadonnées
- ✅ Gestion des permissions (OwnerId, etc.)

### 3. Intégrité des Données
- ✅ Correspondance données créées ↔ extraites
- ✅ Validation des types de champs
- ✅ Vérification des relations
- ✅ Métadonnées Salesforce préservées

### 4. Performance
- ✅ Temps de découverte de schéma < 2 minutes
- ✅ Extraction > 100 enregistrements/seconde
- ✅ Gestion des gros volumes (Bulk API)

## Résolution de Problèmes

### Erreurs de Connexion
```
SalesforceAuthenticationFailed: INVALID_LOGIN
```
- Vérifiez username/password/security_token
- Confirmez que l'IP est autorisée
- Utilisez le bon domaine (login vs test)

### Objets Custom Manquants
```
Objet Playground__c non trouvé dans le schéma
```
- Créez les objets custom dans votre org
- Vérifiez les permissions d'accès
- Confirmez les noms d'API

### Permissions Insuffisantes
```
Permissions insuffisantes pour créer les objets custom
```
- Utilisez un profil System Administrator
- Vérifiez les permissions sur les objets custom
- Activez l'accès API si nécessaire

### Problèmes de Performance
```
Performance d'extraction insuffisante
```
- Vérifiez la connexion réseau
- Utilisez un sandbox proche géographiquement
- Réduisez la taille des batches si nécessaire

## Nettoyage

Le système nettoie automatiquement les données de test, mais vous pouvez aussi :

```python
# Nettoyage manuel
await test_manager.cleanup_test_data()

# Ou via le script
python run_salesforce_tests.py --test-type extraction --no-cleanup
# Puis plus tard :
python -c "
import asyncio
from salesforce_test_manager import SalesforceTestManager
from simple_salesforce import Salesforce

async def cleanup():
    sf = Salesforce(...)
    manager = SalesforceTestManager(sf)
    await manager.cleanup_test_data()

asyncio.run(cleanup())
"
```

## Contribution

Pour ajouter de nouveaux tests :

1. **Nouveaux objets** : Modifiez `custom_objects.py`
2. **Nouvelles données** : Étendez `test_data_generator.py`
3. **Nouvelles validations** : Ajoutez dans `test_salesforce_integration.py`
4. **Nouveaux scénarios** : Créez dans `salesforce_test_manager.py`

## Sécurité

⚠️ **Important** :
- Utilisez TOUJOURS un sandbox ou Developer Edition
- Ne jamais exécuter sur une org de production
- Les données de test sont supprimées automatiquement
- Vérifiez les permissions avant exécution
