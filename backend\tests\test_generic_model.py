"""
Tests for the generic data model.
"""

import pytest
from datetime import datetime
from uuid import uuid4, UUID

from src.core.models import (
    DataSource, EntitySchema, GenericRecord, FieldMetadata, RelationshipMetadata,
    DataType, RelationType, SourceType, ExtractionBatch
)


class TestFieldMetadata:
    """Test FieldMetadata model."""
    
    def test_create_field_metadata(self):
        """Test creating field metadata."""
        field = FieldMetadata(
            name="test_field",
            data_type=DataType.STRING,
            is_required=True,
            max_length=255,
            description="Test field"
        )
        
        assert field.name == "test_field"
        assert field.data_type == DataType.STRING
        assert field.is_required is True
        assert field.max_length == 255
        assert field.description == "Test field"
        assert field.is_sensitive is False  # default


class TestRelationshipMetadata:
    """Test RelationshipMetadata model."""
    
    def test_create_relationship(self):
        """Test creating relationship metadata."""
        rel = RelationshipMetadata(
            name="account_contact",
            relation_type=RelationType.ONE_TO_MANY,
            source_entity="Account",
            target_entity="Contact",
            source_field="Id",
            target_field="AccountId"
        )
        
        assert rel.name == "account_contact"
        assert rel.relation_type == RelationType.ONE_TO_MANY
        assert rel.source_entity == "Account"
        assert rel.target_entity == "Contact"
        assert isinstance(rel.id, UUID)


class TestEntitySchema:
    """Test EntitySchema model."""
    
    def test_create_entity_schema(self):
        """Test creating entity schema."""
        fields = {
            "id": FieldMetadata(name="id", data_type=DataType.STRING),
            "name": FieldMetadata(name="name", data_type=DataType.STRING, is_required=True),
            "email": FieldMetadata(name="email", data_type=DataType.STRING, is_sensitive=True)
        }
        
        relationships = [
            RelationshipMetadata(
                name="contact_account",
                relation_type=RelationType.MANY_TO_ONE,
                source_entity="Contact",
                target_entity="Account",
                source_field="AccountId",
                target_field="Id"
            )
        ]
        
        schema = EntitySchema(
            name="Contact",
            source_type=SourceType.SALESFORCE,
            source_name="Contact",
            description="Contact entity",
            fields=fields,
            primary_key=["id"],
            relationships=relationships
        )
        
        assert schema.name == "Contact"
        assert schema.source_type == SourceType.SALESFORCE
        assert len(schema.fields) == 3
        assert schema.primary_key == ["id"]
        assert len(schema.relationships) == 1
        assert isinstance(schema.created_at, datetime)
    
    def test_primary_key_validation(self):
        """Test that primary key fields must exist in field definitions."""
        fields = {
            "name": FieldMetadata(name="name", data_type=DataType.STRING)
        }
        
        with pytest.raises(ValueError, match="Primary key field 'id' not found"):
            EntitySchema(
                name="Test",
                source_type=SourceType.SALESFORCE,
                source_name="Test",
                fields=fields,
                primary_key=["id"]  # This field doesn't exist
            )


class TestGenericRecord:
    """Test GenericRecord model."""
    
    def test_create_generic_record(self):
        """Test creating a generic record."""
        data = {
            "id": "***************",
            "name": "Test Account",
            "email": "<EMAIL>"
        }
        
        record = GenericRecord(
            entity_name="Account",
            source_type=SourceType.SALESFORCE,
            source_id="***************",
            data=data,
            owner_id="***************"
        )
        
        assert record.entity_name == "Account"
        assert record.source_type == SourceType.SALESFORCE
        assert record.source_id == "***************"
        assert record.data == data
        assert record.owner_id == "***************"
        assert isinstance(record.id, UUID)
        assert isinstance(record.created_at, datetime)
    
    def test_field_operations(self):
        """Test field get/set operations."""
        record = GenericRecord(
            entity_name="Test",
            source_type=SourceType.SALESFORCE,
            source_id="123",
            data={"name": "Test", "value": 42}
        )
        
        # Test get_field_value
        assert record.get_field_value("name") == "Test"
        assert record.get_field_value("value") == 42
        assert record.get_field_value("nonexistent") is None
        
        # Test set_field_value
        original_updated_at = record.updated_at
        record.set_field_value("new_field", "new_value")
        
        assert record.get_field_value("new_field") == "new_value"
        assert record.updated_at > original_updated_at
        
        # Test has_field
        assert record.has_field("name") is True
        assert record.has_field("new_field") is True
        assert record.has_field("nonexistent") is False


class TestDataSource:
    """Test DataSource model."""
    
    def test_create_data_source(self):
        """Test creating a data source."""
        connection_config = {
            "username": "<EMAIL>",
            "password": "password",
            "domain": "login"
        }
        
        source = DataSource(
            name="Test Salesforce",
            source_type=SourceType.SALESFORCE,
            description="Test Salesforce org",
            connection_config=connection_config,
            is_sensitive=True
        )
        
        assert source.name == "Test Salesforce"
        assert source.source_type == SourceType.SALESFORCE
        assert source.connection_config == connection_config
        assert source.is_sensitive is True
        assert isinstance(source.id, UUID)
        assert isinstance(source.created_at, datetime)
    
    def test_entity_schema_operations(self):
        """Test entity schema operations."""
        source = DataSource(
            name="Test",
            source_type=SourceType.SALESFORCE
        )
        
        schema = EntitySchema(
            name="TestEntity",
            source_type=SourceType.SALESFORCE,
            source_name="TestEntity",
            fields={"id": FieldMetadata(name="id", data_type=DataType.STRING)},
            primary_key=["id"]
        )
        
        # Test add_entity_schema
        original_updated_at = source.updated_at
        source.add_entity_schema(schema)
        
        assert "TestEntity" in source.entities
        assert source.entities["TestEntity"] == schema
        assert source.updated_at > original_updated_at
        
        # Test get_entity_schema
        retrieved_schema = source.get_entity_schema("TestEntity")
        assert retrieved_schema == schema
        
        assert source.get_entity_schema("NonExistent") is None
    
    def test_field_exclusion(self):
        """Test field exclusion functionality."""
        source = DataSource(
            name="Test",
            source_type=SourceType.SALESFORCE,
            excluded_fields={
                "Contact": {"SSN", "CreditCard"},
                "Account": {"Revenue"}
            }
        )
        
        assert source.is_field_excluded("Contact", "SSN") is True
        assert source.is_field_excluded("Contact", "Name") is False
        assert source.is_field_excluded("Account", "Revenue") is True
        assert source.is_field_excluded("Account", "Name") is False
        assert source.is_field_excluded("Lead", "SSN") is False


class TestExtractionBatch:
    """Test ExtractionBatch model."""
    
    def test_create_extraction_batch(self):
        """Test creating an extraction batch."""
        source_id = uuid4()
        
        batch = ExtractionBatch(
            source_id=source_id,
            source_name="Test Source"
        )
        
        assert batch.source_id == source_id
        assert batch.source_name == "Test Source"
        assert batch.status == "running"
        assert batch.total_records == 0
        assert batch.error_count == 0
        assert isinstance(batch.id, UUID)
        assert isinstance(batch.started_at, datetime)
    
    def test_batch_operations(self):
        """Test batch operation methods."""
        batch = ExtractionBatch(
            source_id=uuid4(),
            source_name="Test"
        )
        
        # Test add_error
        batch.add_error("Test error")
        assert len(batch.errors) == 1
        assert batch.errors[0] == "Test error"
        assert batch.error_count == 1
        
        # Test add_log
        batch.add_log("Test log message")
        assert len(batch.logs) == 1
        assert "Test log message" in batch.logs[0]
        
        # Test mark_completed
        batch.mark_completed()
        assert batch.status == "completed"
        assert batch.completed_at is not None
        
        # Test mark_failed
        batch2 = ExtractionBatch(source_id=uuid4(), source_name="Test2")
        batch2.mark_failed("Fatal error")
        assert batch2.status == "failed"
        assert batch2.completed_at is not None
        assert "Fatal error" in batch2.errors


class TestModelIntegration:
    """Test integration between different model components."""
    
    def test_full_workflow(self):
        """Test a complete workflow with all model components."""
        # Create data source
        source = DataSource(
            name="Test Salesforce Org",
            source_type=SourceType.SALESFORCE,
            connection_config={"username": "<EMAIL>"},
            excluded_entities={"SystemLog"},
            excluded_fields={"Contact": {"SSN"}}
        )
        
        # Create entity schema
        contact_fields = {
            "Id": FieldMetadata(name="Id", data_type=DataType.STRING),
            "Name": FieldMetadata(name="Name", data_type=DataType.STRING, is_required=True),
            "Email": FieldMetadata(name="Email", data_type=DataType.STRING, is_sensitive=True),
            "AccountId": FieldMetadata(name="AccountId", data_type=DataType.REFERENCE)
        }
        
        contact_relationships = [
            RelationshipMetadata(
                name="contact_account",
                relation_type=RelationType.MANY_TO_ONE,
                source_entity="Contact",
                target_entity="Account",
                source_field="AccountId",
                target_field="Id"
            )
        ]
        
        contact_schema = EntitySchema(
            name="Contact",
            source_type=SourceType.SALESFORCE,
            source_name="Contact",
            fields=contact_fields,
            primary_key=["Id"],
            relationships=contact_relationships,
            is_sensitive=True
        )
        
        # Add schema to source
        source.add_entity_schema(contact_schema)
        
        # Create extraction batch
        batch = ExtractionBatch(
            source_id=source.id,
            source_name=source.name
        )
        
        # Create generic records
        record_data = {
            "Id": "***************",
            "Name": "John Doe",
            "Email": "<EMAIL>",
            "AccountId": "***************"
        }
        
        record = GenericRecord(
            entity_name="Contact",
            source_type=SourceType.SALESFORCE,
            source_id="***************",
            data=record_data,
            extraction_batch_id=batch.id,
            owner_id="***************"
        )
        
        # Verify everything is connected properly
        assert source.get_entity_schema("Contact") == contact_schema
        assert not source.is_field_excluded("Contact", "Name")
        assert source.is_field_excluded("Contact", "SSN")
        assert record.extraction_batch_id == batch.id
        assert record.get_field_value("Name") == "John Doe"
        assert contact_schema.fields["Email"].is_sensitive is True
