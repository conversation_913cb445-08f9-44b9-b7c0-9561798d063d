"""
Generic data manager for orchestrating data extraction and storage.

This module provides the main interface for managing data sources,
extractors, and the generic data model.
"""

from typing import Dict, List, Optional, Type, AsyncIterator
from uuid import UUID
import asyncio
from datetime import datetime
from loguru import logger

from .models import DataSource, GenericRecord, EntitySchema, ExtractionBatch, SourceType
from .extractors import BaseExtractor, ExtractorError
# from .storage import GenericDataStorage  # Will be implemented next


class DataManagerError(Exception):
    """Base exception for data manager errors."""
    pass


class ExtractorRegistry:
    """Registry for managing extractor classes."""
    
    def __init__(self):
        self._extractors: Dict[SourceType, Type[BaseExtractor]] = {}
    
    def register(self, source_type: SourceType, extractor_class: Type[BaseExtractor]):
        """Register an extractor class for a source type."""
        self._extractors[source_type] = extractor_class
        logger.info(f"Registered extractor for {source_type}: {extractor_class.__name__}")
    
    def get_extractor_class(self, source_type: SourceType) -> Optional[Type[BaseExtractor]]:
        """Get extractor class for a source type."""
        return self._extractors.get(source_type)
    
    def get_supported_types(self) -> List[SourceType]:
        """Get list of supported source types."""
        return list(self._extractors.keys())


class DataManager:
    """
    Main data manager for orchestrating data extraction and storage.
    
    This class provides the primary interface for:
    - Managing data sources
    - Running extractions
    - Storing data in the generic model
    - Coordinating with the crunching engine
    """
    
    def __init__(self, storage: GenericDataStorage):
        """
        Initialize the data manager.
        
        Args:
            storage: Storage backend for the generic data model
        """
        self.storage = storage
        self.extractor_registry = ExtractorRegistry()
        self._active_extractions: Dict[UUID, ExtractionBatch] = {}
    
    def register_extractor(self, source_type: SourceType, extractor_class: Type[BaseExtractor]):
        """Register an extractor class."""
        self.extractor_registry.register(source_type, extractor_class)
    
    async def add_data_source(self, source: DataSource) -> None:
        """
        Add a new data source.
        
        Args:
            source: Data source configuration
        """
        # Validate that we have an extractor for this source type
        extractor_class = self.extractor_registry.get_extractor_class(source.source_type)
        if not extractor_class:
            raise DataManagerError(f"No extractor registered for source type: {source.source_type}")
        
        # Test the connection
        extractor = extractor_class(source)
        validation_errors = await extractor.validate_configuration()
        if validation_errors:
            raise DataManagerError(f"Source validation failed: {', '.join(validation_errors)}")
        
        # Store the source
        await self.storage.store_data_source(source)
        logger.info(f"Added data source: {source.name} ({source.source_type})")
    
    async def get_data_source(self, source_id: UUID) -> Optional[DataSource]:
        """Get a data source by ID."""
        return await self.storage.get_data_source(source_id)
    
    async def list_data_sources(self) -> List[DataSource]:
        """Get list of all data sources."""
        return await self.storage.list_data_sources()
    
    async def update_data_source(self, source: DataSource) -> None:
        """Update a data source configuration."""
        source.updated_at = datetime.utcnow()
        await self.storage.store_data_source(source)
        logger.info(f"Updated data source: {source.name}")
    
    async def remove_data_source(self, source_id: UUID) -> None:
        """Remove a data source and all its data."""
        source = await self.get_data_source(source_id)
        if not source:
            raise DataManagerError(f"Data source not found: {source_id}")
        
        # Remove all data for this source
        await self.storage.delete_source_data(source_id)
        await self.storage.delete_data_source(source_id)
        logger.info(f"Removed data source: {source.name}")
    
    async def discover_source_schema(self, source_id: UUID) -> Dict[str, EntitySchema]:
        """
        Discover schema for a data source.
        
        Args:
            source_id: ID of the data source
            
        Returns:
            Dictionary mapping entity names to schemas
        """
        source = await self.get_data_source(source_id)
        if not source:
            raise DataManagerError(f"Data source not found: {source_id}")
        
        extractor_class = self.extractor_registry.get_extractor_class(source.source_type)
        if not extractor_class:
            raise DataManagerError(f"No extractor for source type: {source.source_type}")
        
        async with extractor_class(source) as extractor:
            schemas = await extractor.discover_schema()
            
            # Update source with discovered schemas
            source.entities = schemas
            source.updated_at = datetime.utcnow()
            await self.storage.store_data_source(source)
            
            logger.info(f"Discovered {len(schemas)} entities for source: {source.name}")
            return schemas
    
    async def start_extraction(
        self, 
        source_id: UUID,
        entity_filter: Optional[List[str]] = None,
        batch_size: int = 1000
    ) -> UUID:
        """
        Start data extraction for a source.
        
        Args:
            source_id: ID of the data source
            entity_filter: Optional list of entities to extract
            batch_size: Number of records per batch
            
        Returns:
            Extraction batch ID
        """
        source = await self.get_data_source(source_id)
        if not source:
            raise DataManagerError(f"Data source not found: {source_id}")
        
        extractor_class = self.extractor_registry.get_extractor_class(source.source_type)
        if not extractor_class:
            raise DataManagerError(f"No extractor for source type: {source.source_type}")
        
        # Create extraction batch
        extractor = extractor_class(source)
        batch = extractor.create_extraction_batch()
        self._active_extractions[batch.id] = batch
        
        # Start extraction in background
        asyncio.create_task(self._run_extraction(extractor, batch, entity_filter, batch_size))
        
        logger.info(f"Started extraction for source: {source.name}, batch: {batch.id}")
        return batch.id
    
    async def _run_extraction(
        self,
        extractor: BaseExtractor,
        batch: ExtractionBatch,
        entity_filter: Optional[List[str]],
        batch_size: int
    ) -> None:
        """Run the actual extraction process."""
        try:
            batch.add_log(f"Starting extraction for source: {extractor.source.name}")
            
            async with extractor:
                record_count = 0
                
                async for record in extractor.extract_all_data(batch_size, entity_filter):
                    # Store record
                    await self.storage.store_record(record)
                    record_count += 1
                    batch.total_records = record_count
                    
                    # Log progress periodically
                    if record_count % 1000 == 0:
                        batch.add_log(f"Processed {record_count} records")
                        logger.info(f"Extraction {batch.id}: {record_count} records processed")
            
            # Mark batch as completed
            batch.mark_completed()
            batch.add_log(f"Extraction completed. Total records: {record_count}")
            
            # Update source last extraction time
            extractor.source.last_extraction_at = datetime.utcnow()
            await self.storage.store_data_source(extractor.source)
            
            logger.info(f"Extraction completed: {batch.id}, records: {record_count}")
            
        except Exception as e:
            error_msg = f"Extraction failed: {str(e)}"
            batch.mark_failed(error_msg)
            logger.error(f"Extraction {batch.id} failed: {str(e)}")
        
        finally:
            # Store final batch state
            await self.storage.store_extraction_batch(batch)
            # Remove from active extractions
            if batch.id in self._active_extractions:
                del self._active_extractions[batch.id]
    
    async def get_extraction_status(self, batch_id: UUID) -> Optional[ExtractionBatch]:
        """Get status of an extraction batch."""
        # Check active extractions first
        if batch_id in self._active_extractions:
            return self._active_extractions[batch_id]
        
        # Check storage for completed extractions
        return await self.storage.get_extraction_batch(batch_id)
    
    async def list_extractions(self, source_id: Optional[UUID] = None) -> List[ExtractionBatch]:
        """List extraction batches, optionally filtered by source."""
        return await self.storage.list_extraction_batches(source_id)
    
    async def stop_extraction(self, batch_id: UUID) -> bool:
        """
        Stop an active extraction.
        
        Args:
            batch_id: ID of the extraction batch
            
        Returns:
            True if extraction was stopped, False if not found or already completed
        """
        if batch_id not in self._active_extractions:
            return False
        
        batch = self._active_extractions[batch_id]
        batch.status = "cancelled"
        batch.add_log("Extraction cancelled by user")
        
        # The extraction task will check this status and stop
        logger.info(f"Extraction {batch_id} marked for cancellation")
        return True
    
    async def get_source_statistics(self, source_id: UUID) -> Dict[str, any]:
        """Get statistics for a data source."""
        source = await self.get_data_source(source_id)
        if not source:
            raise DataManagerError(f"Data source not found: {source_id}")
        
        stats = await self.storage.get_source_statistics(source_id)
        
        return {
            "source_name": source.name,
            "source_type": source.source_type,
            "total_entities": len(source.entities),
            "total_records": stats.get("total_records", 0),
            "last_extraction": source.last_extraction_at,
            "entity_counts": stats.get("entity_counts", {}),
            "data_size": stats.get("data_size", 0)
        }
    
    async def cleanup_old_data(self, days_old: int = 30) -> int:
        """
        Clean up old extraction data.
        
        Args:
            days_old: Remove data older than this many days
            
        Returns:
            Number of records removed
        """
        return await self.storage.cleanup_old_data(days_old)
    
    def get_supported_source_types(self) -> List[SourceType]:
        """Get list of supported source types."""
        return self.extractor_registry.get_supported_types()
