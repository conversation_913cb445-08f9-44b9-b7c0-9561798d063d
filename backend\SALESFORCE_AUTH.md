# 🔐 Authentification Salesforce - Guide Complet

Ce guide explique comment configurer l'authentification Salesforce pour FungAI avec vos credentials OAuth.

## 🎯 Vos Credentials

Vous avez fourni les credentials suivants :

```
Consumer Key: 3MVG9XgkMlifdwVDQkkGJ9IxgH73CPRrkBJV1u3UnUx0igOX4XGfUBxVeFH7HqliJ48nQp3B90t9mLMXIYX4b
Consumer Secret: D78423B83FA10BD8DA6B33E616EA3312F41500D1D66CFB49EC3C3F16DAB3AE15
Username: <EMAIL>
Password: FungAi80120
Security Token: cjQiONHkMyY9tFGxr6IVaQF4n
```

## 🚀 Configuration Rapide

### 1. Fichier `.env` (Déjà configuré)

Le fichier `backend/.env` est déjà configuré avec vos credentials :

```bash
# Authentification OAuth (Recommandé)
SF_USERNAME=<EMAIL>
SF_PASSWORD=FungAi80120
SF_SECURITY_TOKEN=cjQiONHkMyY9tFGxr6IVaQF4n
SF_CONSUMER_KEY=3MVG9XgkMlifdwVDQkkGJ9IxgH73CPRrkBJV1u3UnUx0igOX4XGfUBxVeFH7HqliJ48nQp3B90t9mLMXIYX4b
SF_CONSUMER_SECRET=D78423B83FA10BD8DA6B33E616EA3312F41500D1D66CFB49EC3C3F16DAB3AE15
SF_AUTH_TYPE=oauth
SF_DOMAIN=login
SF_API_VERSION=58.0
```

### 2. Test Rapide

```bash
cd backend
python test_auth.py
```

Ce script va :
- ✅ Tester l'authentification OAuth
- ✅ Tester l'authentification Username/Password (fallback)
- ✅ Comparer les performances
- ✅ Valider la connexion à votre org

## 🔄 Types d'Authentification Supportés

### 1. OAuth 2.0 (Recommandé) ⭐

**Avantages :**
- ✅ Plus sécurisé
- ✅ Pas besoin de Security Token
- ✅ Meilleure performance
- ✅ Recommandé par Salesforce

**Configuration :**
```bash
SF_AUTH_TYPE=oauth
SF_CONSUMER_KEY=votre_consumer_key
SF_CONSUMER_SECRET=votre_consumer_secret
SF_USERNAME=votre_username
SF_PASSWORD=votre_password
```

### 2. Username/Password (Fallback)

**Avantages :**
- ✅ Simple à configurer
- ✅ Fonctionne toujours
- ✅ Pas besoin d'app connectée

**Configuration :**
```bash
SF_AUTH_TYPE=password
SF_USERNAME=votre_username
SF_PASSWORD=votre_password
SF_SECURITY_TOKEN=votre_token
```

## 🧪 Tests Disponibles

### 1. Test d'Authentification Rapide
```bash
python test_auth.py
```

### 2. Tests Complets avec Données
```bash
python tests/salesforce_test_data/run_salesforce_tests.py
```

### 3. Tests d'Intégration
```bash
pytest tests/test_salesforce_integration.py -v
```

## 🔧 Résolution de Problèmes

### Erreur : "INVALID_LOGIN"

**Cause :** Credentials incorrects

**Solutions :**
1. Vérifiez username/password
2. Vérifiez le Security Token
3. Confirmez que l'IP est autorisée
4. Utilisez le bon domaine (login vs test)

```bash
# Test avec domaine sandbox
SF_DOMAIN=test

# Test avec domaine production
SF_DOMAIN=login
```

### Erreur : "API_DISABLED_FOR_ORG"

**Cause :** API non activée

**Solutions :**
1. Activez l'API dans Setup → Company Settings → Company Information
2. Vérifiez les permissions utilisateur
3. Contactez votre administrateur Salesforce

### Erreur OAuth : "invalid_client_id"

**Cause :** Consumer Key incorrect

**Solutions :**
1. Vérifiez le Consumer Key dans votre Connected App
2. Confirmez que l'app est approuvée
3. Vérifiez les OAuth Scopes

### Erreur : "invalid_grant"

**Cause :** Password + Security Token incorrect pour OAuth

**Solutions :**
1. Vérifiez que le mot de passe est correct
2. Concaténez password + security_token
3. Régénérez le Security Token si nécessaire

## 🔄 Basculer entre les Modes

### Forcer OAuth
```bash
export SF_AUTH_TYPE=oauth
python test_auth.py
```

### Forcer Password
```bash
export SF_AUTH_TYPE=password
python test_auth.py
```

### Auto-détection
```bash
# Si Consumer Key/Secret présents → OAuth
# Sinon → Password
unset SF_AUTH_TYPE
python test_auth.py
```

## 📊 Comparaison des Performances

| Méthode | Temps Connexion | Sécurité | Complexité |
|---------|----------------|----------|------------|
| OAuth   | ~1-2s          | ⭐⭐⭐⭐⭐    | Moyenne    |
| Password| ~2-3s          | ⭐⭐⭐      | Simple     |

## 🛡️ Sécurité

### Bonnes Pratiques

1. **Utilisez OAuth** quand possible
2. **Protégez vos credentials** - ne les commitez jamais
3. **Utilisez des sandboxes** pour les tests
4. **Régénérez les tokens** régulièrement
5. **Limitez les permissions** de l'utilisateur API

### Variables d'Environnement Sensibles

```bash
# ⚠️ NE JAMAIS COMMITER CES VALEURS
SF_PASSWORD=...
SF_SECURITY_TOKEN=...
SF_CONSUMER_SECRET=...
```

## 🚀 Prochaines Étapes

Une fois l'authentification validée :

1. **Exécuter les tests complets :**
   ```bash
   python tests/salesforce_test_data/run_salesforce_tests.py
   ```

2. **Créer des objets custom** dans votre org (voir `tests/salesforce_test_data/data_model.md`)

3. **Lancer l'extraction complète :**
   ```bash
   python tests/salesforce_test_data/run_salesforce_tests.py --test-type complete
   ```

## 📞 Support

Si vous rencontrez des problèmes :

1. **Vérifiez les logs** : `salesforce_tests.log`
2. **Testez avec mode debug** : `--log-level DEBUG`
3. **Vérifiez votre org Salesforce** dans Setup
4. **Consultez la documentation Salesforce** sur les Connected Apps

## 🎯 Résumé de Votre Configuration

✅ **Username :** <EMAIL>  
✅ **OAuth configuré :** Consumer Key/Secret présents  
✅ **Security Token :** Configuré  
✅ **Domaine :** login (production)  
✅ **API Version :** 58.0  

**Votre configuration est prête ! 🎉**
