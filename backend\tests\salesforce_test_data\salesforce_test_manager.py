"""
Gestionnaire de tests Salesforce pour la validation du modèle générique.

Ce module gère la création d'objets custom, l'insertion de données de test,
et la validation de l'extraction dans Salesforce.
"""

import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from loguru import logger
from simple_salesforce import Salesforce
from simple_salesforce.exceptions import SalesforceError

from .custom_objects import (
    CUSTOM_OBJECTS, get_object_creation_order, get_object_definition,
    CustomObjectDefinition, FieldDefinition
)


class SalesforceTestManager:
    """
    Gestionnaire pour créer et gérer les données de test Salesforce.
    
    Cette classe permet de :
    - Créer les objets custom nécessaires
    - Insérer des données de test cohérentes
    - Valider l'extraction des données
    - Nettoyer les données de test
    """
    
    def __init__(self, sf_connection: Salesforce):
        """
        Initialise le gestionnaire de test.
        
        Args:
            sf_connection: Connexion Salesforce authentifiée
        """
        self.sf = sf_connection
        self.created_objects = []
        self.created_records = {
            'Account': [],
            'Contact': [],
            'Playground__c': [],
            'Equipment__c': [],
            'Maintenance__c': [],
            'Reservation__c': []
        }
    
    async def setup_test_environment(self) -> bool:
        """
        Configure l'environnement de test complet.
        
        Returns:
            True si la configuration est réussie, False sinon
        """
        try:
            logger.info("Début de la configuration de l'environnement de test Salesforce")
            
            # 1. Vérifier les permissions
            if not await self._check_permissions():
                logger.error("Permissions insuffisantes pour créer les objets custom")
                return False
            
            # 2. Créer les objets custom
            if not await self._create_custom_objects():
                logger.error("Échec de la création des objets custom")
                return False
            
            # 3. Attendre que les objets soient disponibles
            await self._wait_for_objects_availability()
            
            # 4. Créer les données de test
            if not await self._create_test_data():
                logger.error("Échec de la création des données de test")
                return False
            
            logger.info("Configuration de l'environnement de test terminée avec succès")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la configuration de l'environnement de test: {str(e)}")
            return False
    
    async def _check_permissions(self) -> bool:
        """Vérifie que l'utilisateur a les permissions nécessaires."""
        try:
            # Vérifier les permissions de base
            user_info = self.sf.query("SELECT Id, Name, Profile.Name FROM User WHERE Id = '{}'".format(
                self.sf.query("SELECT Id FROM User WHERE Username = '{}'".format(
                    self.sf.describe()['organizationId']  # Approximation
                ))['records'][0]['Id']
            ))
            
            logger.info(f"Utilisateur connecté: {user_info['records'][0]['Name']}")
            logger.info(f"Profil: {user_info['records'][0]['Profile']['Name']}")
            
            # Tenter une opération de test
            test_query = self.sf.query("SELECT COUNT() FROM Account LIMIT 1")
            logger.info(f"Test de requête réussi: {test_query['totalSize']} comptes trouvés")
            
            return True
            
        except Exception as e:
            logger.error(f"Vérification des permissions échouée: {str(e)}")
            return False
    
    async def _create_custom_objects(self) -> bool:
        """Crée tous les objets custom nécessaires."""
        try:
            creation_order = get_object_creation_order()
            
            for object_api_name in creation_order:
                obj_def = get_object_definition(object_api_name)
                
                if await self._object_exists(object_api_name):
                    logger.info(f"L'objet {object_api_name} existe déjà")
                    continue
                
                logger.info(f"Création de l'objet custom: {object_api_name}")
                
                if await self._create_single_custom_object(obj_def):
                    self.created_objects.append(object_api_name)
                    logger.info(f"Objet {object_api_name} créé avec succès")
                else:
                    logger.error(f"Échec de la création de l'objet {object_api_name}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la création des objets custom: {str(e)}")
            return False
    
    async def _object_exists(self, object_api_name: str) -> bool:
        """Vérifie si un objet custom existe déjà."""
        try:
            # Tenter de décrire l'objet
            getattr(self.sf, object_api_name).describe()
            return True
        except Exception:
            return False
    
    async def _create_single_custom_object(self, obj_def: CustomObjectDefinition) -> bool:
        """
        Crée un seul objet custom.
        
        Note: Cette méthode utilise l'API Metadata de Salesforce.
        En réalité, la création d'objets custom via l'API nécessite
        des permissions spéciales et l'utilisation de l'API Metadata.
        
        Pour les tests, nous assumons que les objets sont créés manuellement
        ou via un package/deployment.
        """
        try:
            # Dans un environnement réel, nous utiliserions l'API Metadata
            # Pour ce test, nous simulons la création
            logger.warning(f"Simulation de la création de l'objet {obj_def.api_name}")
            logger.info(f"Objet: {obj_def.label} ({obj_def.api_name})")
            logger.info(f"Description: {obj_def.description}")
            logger.info(f"Nombre de champs: {len(obj_def.fields)}")
            
            # Afficher les champs pour validation
            for field in obj_def.fields:
                logger.debug(f"  - {field.name} ({field.type}): {field.label}")
            
            # Dans un vrai scénario, nous créerions l'objet ici
            # return self._create_object_via_metadata_api(obj_def)
            
            # Pour le test, nous retournons True en assumant que l'objet existe
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la création de l'objet {obj_def.api_name}: {str(e)}")
            return False
    
    async def _wait_for_objects_availability(self):
        """Attend que les objets custom soient disponibles."""
        logger.info("Attente de la disponibilité des objets custom...")
        
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                all_available = True
                
                for object_api_name in get_object_creation_order():
                    try:
                        # Tenter de décrire l'objet
                        getattr(self.sf, object_api_name).describe()
                    except Exception:
                        all_available = False
                        break
                
                if all_available:
                    logger.info("Tous les objets custom sont disponibles")
                    return
                
                logger.info(f"Tentative {attempt + 1}/{max_attempts} - Objets pas encore disponibles")
                time.sleep(2)
                
            except Exception as e:
                logger.warning(f"Erreur lors de la vérification de disponibilité: {str(e)}")
                time.sleep(2)
        
        logger.warning("Timeout atteint - Poursuite du processus")
    
    async def _create_test_data(self) -> bool:
        """Crée toutes les données de test."""
        try:
            logger.info("Début de la création des données de test")
            
            # 1. Créer les comptes (organisations)
            if not await self._create_test_accounts():
                return False
            
            # 2. Créer les contacts
            if not await self._create_test_contacts():
                return False
            
            # 3. Créer les terrains
            if not await self._create_test_playgrounds():
                return False
            
            # 4. Créer les équipements
            if not await self._create_test_equipment():
                return False
            
            # 5. Créer les maintenances
            if not await self._create_test_maintenance():
                return False
            
            # 6. Créer les réservations
            if not await self._create_test_reservations():
                return False
            
            logger.info("Création des données de test terminée avec succès")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la création des données de test: {str(e)}")
            return False
    
    async def _create_test_accounts(self) -> bool:
        """Crée les comptes de test."""
        try:
            accounts_data = [
                {
                    'Name': 'Club Sportif Municipal',
                    'Type': 'Other',
                    'Phone': '***********.89',
                    'Website': 'https://club-municipal.fr',
                    'Description': 'Club sportif géré par la municipalité, offrant diverses activités sportives',
                    'BillingStreet': '123 Avenue des Sports',
                    'BillingCity': 'Paris',
                    'BillingPostalCode': '75001',
                    'BillingCountry': 'France'
                },
                {
                    'Name': 'Tennis Club Elite',
                    'Type': 'Customer - Direct',
                    'Phone': '***********.90',
                    'Website': 'https://tennis-elite.fr',
                    'Description': 'Club de tennis privé haut de gamme avec installations premium',
                    'BillingStreet': '456 Rue du Tennis',
                    'BillingCity': 'Neuilly-sur-Seine',
                    'BillingPostalCode': '92200',
                    'BillingCountry': 'France'
                },
                {
                    'Name': 'Centre Sportif Universitaire',
                    'Type': 'Other',
                    'Phone': '***********.01',
                    'Website': 'https://sport-univ.edu',
                    'Description': 'Centre sportif universitaire ouvert aux étudiants et personnel',
                    'BillingStreet': '789 Campus Universitaire',
                    'BillingCity': 'Orsay',
                    'BillingPostalCode': '91400',
                    'BillingCountry': 'France'
                },
                {
                    'Name': 'Association Quartier Sud',
                    'Type': 'Other',
                    'Phone': '***********.12',
                    'Description': 'Association de quartier gérant des équipements sportifs locaux',
                    'BillingStreet': '321 Place du Quartier',
                    'BillingCity': 'Lyon',
                    'BillingPostalCode': '69007',
                    'BillingCountry': 'France'
                },
                {
                    'Name': 'Complexe Sportif Privé',
                    'Type': 'Customer - Direct',
                    'Phone': '***********.23',
                    'Website': 'https://complexe-prive.com',
                    'Description': 'Complexe sportif privé avec installations multisports',
                    'BillingStreet': '654 Boulevard du Sport',
                    'BillingCity': 'Marseille',
                    'BillingPostalCode': '13008',
                    'BillingCountry': 'France'
                }
            ]
            
            logger.info(f"Création de {len(accounts_data)} comptes de test")
            
            for account_data in accounts_data:
                try:
                    result = self.sf.Account.create(account_data)
                    if result['success']:
                        self.created_records['Account'].append(result['id'])
                        logger.debug(f"Compte créé: {account_data['Name']} (ID: {result['id']})")
                    else:
                        logger.error(f"Échec de création du compte {account_data['Name']}: {result}")
                        return False
                        
                except Exception as e:
                    logger.error(f"Erreur lors de la création du compte {account_data['Name']}: {str(e)}")
                    return False
            
            logger.info(f"{len(self.created_records['Account'])} comptes créés avec succès")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la création des comptes: {str(e)}")
            return False
    
    async def _create_test_contacts(self) -> bool:
        """Crée les contacts de test."""
        try:
            # Récupérer les IDs des comptes créés pour les associations
            account_ids = self.created_records['Account']
            
            contacts_data = [
                {
                    'FirstName': 'Jean',
                    'LastName': 'Dupont',
                    'Email': '<EMAIL>',
                    'Phone': '***********.78',
                    'Birthdate': '1985-03-15',
                    'AccountId': account_ids[0] if account_ids else None,
                    'MailingStreet': '12 Rue de la Paix',
                    'MailingCity': 'Paris',
                    'MailingPostalCode': '75001',
                    'MailingCountry': 'France',
                    'Description': 'Joueur régulier de football'
                },
                {
                    'FirstName': 'Marie',
                    'LastName': 'Martin',
                    'Email': '<EMAIL>',
                    'Phone': '***********.89',
                    'Birthdate': '1990-07-22',
                    'AccountId': account_ids[1] if len(account_ids) > 1 else None,
                    'MailingStreet': '34 Avenue des Fleurs',
                    'MailingCity': 'Lyon',
                    'MailingPostalCode': '69001',
                    'MailingCountry': 'France',
                    'Description': 'Passionnée de tennis'
                },
                {
                    'FirstName': 'Pierre',
                    'LastName': 'Durand',
                    'Email': '<EMAIL>',
                    'Phone': '***********.90',
                    'Birthdate': '1988-11-10',
                    'MailingStreet': '56 Boulevard du Sport',
                    'MailingCity': 'Marseille',
                    'MailingPostalCode': '13001',
                    'MailingCountry': 'France',
                    'Description': 'Joueur de basketball amateur'
                },
                {
                    'FirstName': 'Sophie',
                    'LastName': 'Leroy',
                    'Email': '<EMAIL>',
                    'Phone': '***********.01',
                    'Birthdate': '1992-05-18',
                    'AccountId': account_ids[2] if len(account_ids) > 2 else None,
                    'MailingStreet': '78 Rue du Campus',
                    'MailingCity': 'Orsay',
                    'MailingPostalCode': '91400',
                    'MailingCountry': 'France',
                    'Description': 'Étudiante pratiquant le volleyball'
                },
                {
                    'FirstName': 'Thomas',
                    'LastName': 'Moreau',
                    'Email': '<EMAIL>',
                    'Phone': '***********.12',
                    'Birthdate': '1987-09-03',
                    'MailingStreet': '90 Place Centrale',
                    'MailingCity': 'Toulouse',
                    'MailingPostalCode': '31000',
                    'MailingCountry': 'France',
                    'Description': 'Organisateur d\'événements sportifs'
                }
            ]
            
            logger.info(f"Création de {len(contacts_data)} contacts de test")
            
            for contact_data in contacts_data:
                try:
                    result = self.sf.Contact.create(contact_data)
                    if result['success']:
                        self.created_records['Contact'].append(result['id'])
                        logger.debug(f"Contact créé: {contact_data['FirstName']} {contact_data['LastName']} (ID: {result['id']})")
                    else:
                        logger.error(f"Échec de création du contact {contact_data['FirstName']} {contact_data['LastName']}: {result}")
                        return False
                        
                except Exception as e:
                    logger.error(f"Erreur lors de la création du contact {contact_data['FirstName']} {contact_data['LastName']}: {str(e)}")
                    return False
            
            logger.info(f"{len(self.created_records['Contact'])} contacts créés avec succès")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors de la création des contacts: {str(e)}")
            return False
    
    def get_created_records_summary(self) -> Dict[str, Any]:
        """Retourne un résumé des enregistrements créés."""
        summary = {}
        for object_type, record_ids in self.created_records.items():
            summary[object_type] = {
                'count': len(record_ids),
                'ids': record_ids
            }
        
        return summary
    
    async def cleanup_test_data(self) -> bool:
        """Nettoie toutes les données de test créées."""
        try:
            logger.info("Début du nettoyage des données de test")
            
            # Supprimer dans l'ordre inverse des dépendances
            cleanup_order = [
                'Reservation__c',
                'Maintenance__c', 
                'Equipment__c',
                'Playground__c',
                'Contact',
                'Account'
            ]
            
            for object_type in cleanup_order:
                if object_type in self.created_records and self.created_records[object_type]:
                    await self._delete_records(object_type, self.created_records[object_type])
            
            logger.info("Nettoyage des données de test terminé")
            return True
            
        except Exception as e:
            logger.error(f"Erreur lors du nettoyage: {str(e)}")
            return False
    
    async def _delete_records(self, object_type: str, record_ids: List[str]):
        """Supprime une liste d'enregistrements."""
        try:
            logger.info(f"Suppression de {len(record_ids)} enregistrements {object_type}")
            
            for record_id in record_ids:
                try:
                    if object_type.endswith('__c'):
                        # Objet custom
                        getattr(self.sf, object_type).delete(record_id)
                    else:
                        # Objet standard
                        getattr(self.sf, object_type).delete(record_id)
                    
                    logger.debug(f"Enregistrement supprimé: {object_type} {record_id}")
                    
                except Exception as e:
                    logger.warning(f"Erreur lors de la suppression de {object_type} {record_id}: {str(e)}")
            
            # Vider la liste après suppression
            self.created_records[object_type] = []
            
        except Exception as e:
            logger.error(f"Erreur lors de la suppression des enregistrements {object_type}: {str(e)}")

    async def _create_test_playgrounds(self) -> bool:
        """Crée les terrains de test."""
        try:
            account_ids = self.created_records['Account']
            if not account_ids:
                logger.error("Aucun compte disponible pour créer les terrains")
                return False

            playgrounds_data = [
                {
                    'Name': 'Terrain Football Principal',
                    'Account__c': account_ids[0],
                    'Playground_Type__c': 'Football',
                    'Surface_Type__c': 'Gazon naturel',
                    'Capacity__c': 22,
                    'Hourly_Rate__c': 50.00,
                    'Is_Active__c': True,
                    'Equipment_Available__c': 'Ballons;Filets;Éclairage;Vestiaires;Douches',
                    'Description__c': 'Terrain de football principal avec gazon naturel et éclairage',
                    'Opening_Hours__c': '8h00-22h00',
                    'Special_Rules__c': 'Chaussures à crampons interdites sur terrain synthétique'
                },
                {
                    'Name': 'Court Tennis N°1',
                    'Account__c': account_ids[1] if len(account_ids) > 1 else account_ids[0],
                    'Playground_Type__c': 'Tennis',
                    'Surface_Type__c': 'Terre battue',
                    'Capacity__c': 4,
                    'Hourly_Rate__c': 25.00,
                    'Is_Active__c': True,
                    'Equipment_Available__c': 'Filets;Raquettes;Éclairage;Vestiaires',
                    'Description__c': 'Court de tennis en terre battue avec éclairage nocturne',
                    'Opening_Hours__c': '7h00-23h00',
                    'Special_Rules__c': 'Réservation minimum 1h, maximum 3h consécutives'
                },
                {
                    'Name': 'Terrain Basketball Extérieur',
                    'Account__c': account_ids[2] if len(account_ids) > 2 else account_ids[0],
                    'Playground_Type__c': 'Basketball',
                    'Surface_Type__c': 'Béton',
                    'Capacity__c': 10,
                    'Hourly_Rate__c': 20.00,
                    'Is_Active__c': True,
                    'Equipment_Available__c': 'Ballons;Tableau de score;Éclairage',
                    'Description__c': 'Terrain de basketball extérieur avec paniers réglementaires',
                    'Opening_Hours__c': '6h00-22h00',
                    'Special_Rules__c': 'Accès libre en journée, réservation obligatoire le soir'
                },
                {
                    'Name': 'Terrain Multi-sport Couvert',
                    'Account__c': account_ids[3] if len(account_ids) > 3 else account_ids[0],
                    'Playground_Type__c': 'Multi-sport',
                    'Surface_Type__c': 'Parquet',
                    'Capacity__c': 20,
                    'Hourly_Rate__c': 40.00,
                    'Is_Active__c': True,
                    'Equipment_Available__c': 'Ballons;Filets;Raquettes;Vestiaires;Douches;Gradins',
                    'Description__c': 'Salle multi-sport couverte adaptée à plusieurs disciplines',
                    'Opening_Hours__c': '8h00-23h00',
                    'Special_Rules__c': 'Chaussures de sport obligatoires, pas de chaussures de ville'
                },
                {
                    'Name': 'Court Tennis N°2',
                    'Account__c': account_ids[1] if len(account_ids) > 1 else account_ids[0],
                    'Playground_Type__c': 'Tennis',
                    'Surface_Type__c': 'Gazon synthétique',
                    'Capacity__c': 4,
                    'Hourly_Rate__c': 30.00,
                    'Is_Active__c': True,
                    'Equipment_Available__c': 'Filets;Éclairage;Vestiaires',
                    'Description__c': 'Court de tennis en gazon synthétique, idéal pour débutants',
                    'Opening_Hours__c': '7h00-23h00'
                }
            ]

            logger.info(f"Création de {len(playgrounds_data)} terrains de test")

            for playground_data in playgrounds_data:
                try:
                    result = self.sf.Playground__c.create(playground_data)
                    if result['success']:
                        self.created_records['Playground__c'].append(result['id'])
                        logger.debug(f"Terrain créé: {playground_data['Name']} (ID: {result['id']})")
                    else:
                        logger.error(f"Échec de création du terrain {playground_data['Name']}: {result}")
                        return False

                except Exception as e:
                    logger.error(f"Erreur lors de la création du terrain {playground_data['Name']}: {str(e)}")
                    return False

            logger.info(f"{len(self.created_records['Playground__c'])} terrains créés avec succès")
            return True

        except Exception as e:
            logger.error(f"Erreur lors de la création des terrains: {str(e)}")
            return False

    async def _create_test_equipment(self) -> bool:
        """Crée les équipements de test."""
        try:
            playground_ids = self.created_records['Playground__c']
            if not playground_ids:
                logger.error("Aucun terrain disponible pour créer les équipements")
                return False

            equipment_data = [
                {
                    'Name': 'Ballons de Football',
                    'Equipment_Type__c': 'Ballon Football',
                    'Playground__c': playground_ids[0],
                    'Quantity_Available__c': 10,
                    'Condition__c': 'Bon',
                    'Purchase_Date__c': '2023-01-15',
                    'Is_Available__c': True
                },
                {
                    'Name': 'Raquettes de Tennis',
                    'Equipment_Type__c': 'Raquette Tennis',
                    'Playground__c': playground_ids[1] if len(playground_ids) > 1 else playground_ids[0],
                    'Quantity_Available__c': 8,
                    'Condition__c': 'Excellent',
                    'Purchase_Date__c': '2023-03-10',
                    'Is_Available__c': True,
                    'Rental_Rate__c': 5.00
                },
                {
                    'Name': 'Ballons de Basketball',
                    'Equipment_Type__c': 'Ballon Basketball',
                    'Playground__c': playground_ids[2] if len(playground_ids) > 2 else playground_ids[0],
                    'Quantity_Available__c': 6,
                    'Condition__c': 'Bon',
                    'Purchase_Date__c': '2023-02-20',
                    'Is_Available__c': True
                },
                {
                    'Name': 'Filets de Volleyball',
                    'Equipment_Type__c': 'Filet Volleyball',
                    'Playground__c': playground_ids[3] if len(playground_ids) > 3 else playground_ids[0],
                    'Quantity_Available__c': 2,
                    'Condition__c': 'Excellent',
                    'Purchase_Date__c': '2023-04-05',
                    'Is_Available__c': True
                }
            ]

            logger.info(f"Création de {len(equipment_data)} équipements de test")

            for equipment in equipment_data:
                try:
                    result = self.sf.Equipment__c.create(equipment)
                    if result['success']:
                        self.created_records['Equipment__c'].append(result['id'])
                        logger.debug(f"Équipement créé: {equipment['Name']} (ID: {result['id']})")
                    else:
                        logger.error(f"Échec de création de l'équipement {equipment['Name']}: {result}")
                        return False

                except Exception as e:
                    logger.error(f"Erreur lors de la création de l'équipement {equipment['Name']}: {str(e)}")
                    return False

            logger.info(f"{len(self.created_records['Equipment__c'])} équipements créés avec succès")
            return True

        except Exception as e:
            logger.error(f"Erreur lors de la création des équipements: {str(e)}")
            return False

    async def _create_test_maintenance(self) -> bool:
        """Crée les maintenances de test."""
        try:
            playground_ids = self.created_records['Playground__c']
            if not playground_ids:
                logger.error("Aucun terrain disponible pour créer les maintenances")
                return False

            # Dates pour les maintenances (passées, présentes, futures)
            today = datetime.now().date()

            maintenance_data = [
                {
                    'Playground__c': playground_ids[0],
                    'Maintenance_Type__c': 'Préventive',
                    'Scheduled_Date__c': (today - timedelta(days=30)).isoformat(),
                    'Completed_Date__c': (today - timedelta(days=28)).isoformat(),
                    'Status__c': 'Terminée',
                    'Description__c': 'Entretien mensuel du gazon et vérification des équipements',
                    'Cost__c': 150.00,
                    'Technician__c': 'Jean Jardinier',
                    'Impact_on_Reservations__c': False
                },
                {
                    'Playground__c': playground_ids[1] if len(playground_ids) > 1 else playground_ids[0],
                    'Maintenance_Type__c': 'Corrective',
                    'Scheduled_Date__c': (today - timedelta(days=15)).isoformat(),
                    'Completed_Date__c': (today - timedelta(days=14)).isoformat(),
                    'Status__c': 'Terminée',
                    'Description__c': 'Réparation du filet de tennis et remplacement des balles usagées',
                    'Cost__c': 75.00,
                    'Technician__c': 'Marie Réparatrice',
                    'Impact_on_Reservations__c': True
                },
                {
                    'Playground__c': playground_ids[2] if len(playground_ids) > 2 else playground_ids[0],
                    'Maintenance_Type__c': 'Préventive',
                    'Scheduled_Date__c': (today + timedelta(days=7)).isoformat(),
                    'Status__c': 'Planifiée',
                    'Description__c': 'Nettoyage du terrain et vérification des paniers de basketball',
                    'Cost__c': 100.00,
                    'Technician__c': 'Pierre Nettoyeur',
                    'Impact_on_Reservations__c': False
                },
                {
                    'Playground__c': playground_ids[0],
                    'Maintenance_Type__c': 'Saisonnière',
                    'Scheduled_Date__c': (today + timedelta(days=45)).isoformat(),
                    'Status__c': 'Planifiée',
                    'Description__c': 'Préparation hivernale du terrain de football',
                    'Cost__c': 300.00,
                    'Technician__c': 'Équipe Maintenance',
                    'Impact_on_Reservations__c': True
                }
            ]

            logger.info(f"Création de {len(maintenance_data)} maintenances de test")

            for maintenance in maintenance_data:
                try:
                    result = self.sf.Maintenance__c.create(maintenance)
                    if result['success']:
                        self.created_records['Maintenance__c'].append(result['id'])
                        logger.debug(f"Maintenance créée: {maintenance['Maintenance_Type__c']} (ID: {result['id']})")
                    else:
                        logger.error(f"Échec de création de la maintenance: {result}")
                        return False

                except Exception as e:
                    logger.error(f"Erreur lors de la création de la maintenance: {str(e)}")
                    return False

            logger.info(f"{len(self.created_records['Maintenance__c'])} maintenances créées avec succès")
            return True

        except Exception as e:
            logger.error(f"Erreur lors de la création des maintenances: {str(e)}")
            return False

    async def _create_test_reservations(self) -> bool:
        """Crée les réservations de test."""
        try:
            contact_ids = self.created_records['Contact']
            playground_ids = self.created_records['Playground__c']

            if not contact_ids or not playground_ids:
                logger.error("Contacts ou terrains manquants pour créer les réservations")
                return False

            # Générer des réservations variées (passées, présentes, futures)
            today = datetime.now()

            reservations_data = [
                # Réservations passées
                {
                    'Contact__c': contact_ids[0],
                    'Playground__c': playground_ids[0],
                    'Reservation_Date__c': (today - timedelta(days=10)).date().isoformat(),
                    'Start_Time__c': (today - timedelta(days=10)).replace(hour=14, minute=0, second=0).isoformat(),
                    'End_Time__c': (today - timedelta(days=10)).replace(hour=16, minute=0, second=0).isoformat(),
                    'Duration_Hours__c': 2.0,
                    'Status__c': 'Terminée',
                    'Total_Amount__c': 100.00,
                    'Payment_Status__c': 'Payé',
                    'Number_of_Players__c': 18,
                    'Special_Requests__c': 'Match amical entre équipes locales'
                },
                {
                    'Contact__c': contact_ids[1] if len(contact_ids) > 1 else contact_ids[0],
                    'Playground__c': playground_ids[1] if len(playground_ids) > 1 else playground_ids[0],
                    'Reservation_Date__c': (today - timedelta(days=5)).date().isoformat(),
                    'Start_Time__c': (today - timedelta(days=5)).replace(hour=10, minute=0, second=0).isoformat(),
                    'End_Time__c': (today - timedelta(days=5)).replace(hour=11, minute=30, second=0).isoformat(),
                    'Duration_Hours__c': 1.5,
                    'Status__c': 'Terminée',
                    'Total_Amount__c': 37.50,
                    'Payment_Status__c': 'Payé',
                    'Number_of_Players__c': 2,
                    'Special_Requests__c': 'Cours particulier de tennis'
                },
                # Réservations présentes/futures
                {
                    'Contact__c': contact_ids[2] if len(contact_ids) > 2 else contact_ids[0],
                    'Playground__c': playground_ids[2] if len(playground_ids) > 2 else playground_ids[0],
                    'Reservation_Date__c': today.date().isoformat(),
                    'Start_Time__c': today.replace(hour=18, minute=0, second=0).isoformat(),
                    'End_Time__c': today.replace(hour=20, minute=0, second=0).isoformat(),
                    'Duration_Hours__c': 2.0,
                    'Status__c': 'Confirmée',
                    'Total_Amount__c': 40.00,
                    'Payment_Status__c': 'Payé',
                    'Number_of_Players__c': 8,
                    'Special_Requests__c': 'Entraînement équipe junior'
                },
                {
                    'Contact__c': contact_ids[3] if len(contact_ids) > 3 else contact_ids[0],
                    'Playground__c': playground_ids[3] if len(playground_ids) > 3 else playground_ids[0],
                    'Reservation_Date__c': (today + timedelta(days=3)).date().isoformat(),
                    'Start_Time__c': (today + timedelta(days=3)).replace(hour=19, minute=0, second=0).isoformat(),
                    'End_Time__c': (today + timedelta(days=3)).replace(hour=21, minute=0, second=0).isoformat(),
                    'Duration_Hours__c': 2.0,
                    'Status__c': 'Confirmée',
                    'Total_Amount__c': 80.00,
                    'Payment_Status__c': 'En attente',
                    'Number_of_Players__c': 12,
                    'Special_Requests__c': 'Tournoi inter-entreprises'
                },
                {
                    'Contact__c': contact_ids[4] if len(contact_ids) > 4 else contact_ids[0],
                    'Playground__c': playground_ids[1] if len(playground_ids) > 1 else playground_ids[0],
                    'Reservation_Date__c': (today + timedelta(days=7)).date().isoformat(),
                    'Start_Time__c': (today + timedelta(days=7)).replace(hour=16, minute=0, second=0).isoformat(),
                    'End_Time__c': (today + timedelta(days=7)).replace(hour=17, minute=0, second=0).isoformat(),
                    'Duration_Hours__c': 1.0,
                    'Status__c': 'En attente',
                    'Total_Amount__c': 30.00,
                    'Payment_Status__c': 'En attente',
                    'Number_of_Players__c': 2,
                    'Special_Requests__c': 'Match de tennis en double'
                }
            ]

            logger.info(f"Création de {len(reservations_data)} réservations de test")

            for reservation in reservations_data:
                try:
                    result = self.sf.Reservation__c.create(reservation)
                    if result['success']:
                        self.created_records['Reservation__c'].append(result['id'])
                        logger.debug(f"Réservation créée: {reservation['Status__c']} (ID: {result['id']})")
                    else:
                        logger.error(f"Échec de création de la réservation: {result}")
                        return False

                except Exception as e:
                    logger.error(f"Erreur lors de la création de la réservation: {str(e)}")
                    return False

            logger.info(f"{len(self.created_records['Reservation__c'])} réservations créées avec succès")
            return True

        except Exception as e:
            logger.error(f"Erreur lors de la création des réservations: {str(e)}")
            return False
