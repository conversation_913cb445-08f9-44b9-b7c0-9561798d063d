"""
Generic data storage interface and implementations.

This module provides the storage layer for the generic data model,
supporting different storage backends while maintaining a consistent interface.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
from uuid import UUID
from datetime import datetime, timedelta

from .models import DataSource, GenericRecord, EntitySchema, ExtractionBatch


class StorageError(Exception):
    """Base exception for storage errors."""
    pass


class GenericDataStorage(ABC):
    """
    Abstract base class for generic data storage.
    
    This interface defines the methods that all storage implementations
    must provide for storing and retrieving generic data model objects.
    """
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the storage backend."""
        pass
    
    @abstractmethod
    async def close(self) -> None:
        """Close the storage backend."""
        pass
    
    # Data Source operations
    @abstractmethod
    async def store_data_source(self, source: DataSource) -> None:
        """Store a data source configuration."""
        pass
    
    @abstractmethod
    async def get_data_source(self, source_id: UUID) -> Optional[DataSource]:
        """Get a data source by ID."""
        pass
    
    @abstractmethod
    async def list_data_sources(self) -> List[DataSource]:
        """List all data sources."""
        pass
    
    @abstractmethod
    async def delete_data_source(self, source_id: UUID) -> None:
        """Delete a data source configuration."""
        pass
    
    # Record operations
    @abstractmethod
    async def store_record(self, record: GenericRecord) -> None:
        """Store a generic record."""
        pass
    
    @abstractmethod
    async def store_records_batch(self, records: List[GenericRecord]) -> None:
        """Store multiple records in a batch."""
        pass
    
    @abstractmethod
    async def get_record(self, record_id: UUID) -> Optional[GenericRecord]:
        """Get a record by ID."""
        pass
    
    @abstractmethod
    async def get_records_by_entity(
        self, 
        entity_name: str, 
        source_id: Optional[UUID] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[GenericRecord]:
        """Get records for a specific entity."""
        pass
    
    @abstractmethod
    async def get_records_by_source(
        self, 
        source_id: UUID,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[GenericRecord]:
        """Get all records for a specific source."""
        pass
    
    @abstractmethod
    async def delete_record(self, record_id: UUID) -> None:
        """Delete a record."""
        pass
    
    @abstractmethod
    async def delete_source_data(self, source_id: UUID) -> None:
        """Delete all data for a specific source."""
        pass
    
    # Extraction batch operations
    @abstractmethod
    async def store_extraction_batch(self, batch: ExtractionBatch) -> None:
        """Store an extraction batch."""
        pass
    
    @abstractmethod
    async def get_extraction_batch(self, batch_id: UUID) -> Optional[ExtractionBatch]:
        """Get an extraction batch by ID."""
        pass
    
    @abstractmethod
    async def list_extraction_batches(self, source_id: Optional[UUID] = None) -> List[ExtractionBatch]:
        """List extraction batches, optionally filtered by source."""
        pass
    
    # Statistics and analytics
    @abstractmethod
    async def get_source_statistics(self, source_id: UUID) -> Dict[str, Any]:
        """Get statistics for a data source."""
        pass
    
    @abstractmethod
    async def get_entity_statistics(self, entity_name: str, source_id: Optional[UUID] = None) -> Dict[str, Any]:
        """Get statistics for an entity."""
        pass
    
    @abstractmethod
    async def cleanup_old_data(self, days_old: int) -> int:
        """Clean up old data and return number of records removed."""
        pass
    
    # Search and filtering
    @abstractmethod
    async def search_records(
        self,
        query: str,
        entity_filter: Optional[List[str]] = None,
        source_filter: Optional[List[UUID]] = None,
        limit: Optional[int] = None
    ) -> List[GenericRecord]:
        """Search records by text query."""
        pass
    
    @abstractmethod
    async def filter_records(
        self,
        filters: Dict[str, Any],
        entity_name: Optional[str] = None,
        source_id: Optional[UUID] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[GenericRecord]:
        """Filter records by field values."""
        pass


class PostgreSQLStorage(GenericDataStorage):
    """
    PostgreSQL implementation of generic data storage.
    
    This implementation uses PostgreSQL with JSONB columns for flexible
    schema storage while maintaining good performance.
    """
    
    def __init__(self, connection_string: str):
        """
        Initialize PostgreSQL storage.
        
        Args:
            connection_string: PostgreSQL connection string
        """
        self.connection_string = connection_string
        self._pool = None
    
    async def initialize(self) -> None:
        """Initialize PostgreSQL connection pool and create tables."""
        import asyncpg
        
        # Create connection pool
        self._pool = await asyncpg.create_pool(self.connection_string)
        
        # Create tables
        await self._create_tables()
    
    async def close(self) -> None:
        """Close the connection pool."""
        if self._pool:
            await self._pool.close()
    
    async def _create_tables(self) -> None:
        """Create necessary tables if they don't exist."""
        async with self._pool.acquire() as conn:
            # Data sources table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS data_sources (
                    id UUID PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    source_type VARCHAR(50) NOT NULL,
                    description TEXT,
                    connection_config JSONB NOT NULL DEFAULT '{}',
                    extraction_config JSONB NOT NULL DEFAULT '{}',
                    is_enabled BOOLEAN NOT NULL DEFAULT true,
                    is_sensitive BOOLEAN NOT NULL DEFAULT false,
                    excluded_entities JSONB NOT NULL DEFAULT '[]',
                    excluded_fields JSONB NOT NULL DEFAULT '{}',
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    last_extraction_at TIMESTAMP WITH TIME ZONE,
                    entities JSONB NOT NULL DEFAULT '{}'
                )
            """)
            
            # Generic records table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS generic_records (
                    id UUID PRIMARY KEY,
                    entity_name VARCHAR(255) NOT NULL,
                    source_type VARCHAR(50) NOT NULL,
                    source_id UUID NOT NULL REFERENCES data_sources(id) ON DELETE CASCADE,
                    source_record_id VARCHAR(255) NOT NULL,
                    data JSONB NOT NULL DEFAULT '{}',
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    source_created_at TIMESTAMP WITH TIME ZONE,
                    source_updated_at TIMESTAMP WITH TIME ZONE,
                    owner_id VARCHAR(255),
                    created_by_id VARCHAR(255),
                    last_modified_by_id VARCHAR(255),
                    extraction_batch_id UUID,
                    is_processed BOOLEAN NOT NULL DEFAULT false,
                    processing_errors JSONB NOT NULL DEFAULT '[]',
                    source_metadata JSONB NOT NULL DEFAULT '{}'
                )
            """)
            
            # Extraction batches table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS extraction_batches (
                    id UUID PRIMARY KEY,
                    source_id UUID NOT NULL REFERENCES data_sources(id) ON DELETE CASCADE,
                    source_name VARCHAR(255) NOT NULL,
                    started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    completed_at TIMESTAMP WITH TIME ZONE,
                    status VARCHAR(50) NOT NULL DEFAULT 'running',
                    total_entities INTEGER NOT NULL DEFAULT 0,
                    processed_entities INTEGER NOT NULL DEFAULT 0,
                    total_records INTEGER NOT NULL DEFAULT 0,
                    error_count INTEGER NOT NULL DEFAULT 0,
                    extraction_config JSONB NOT NULL DEFAULT '{}',
                    errors JSONB NOT NULL DEFAULT '[]',
                    logs JSONB NOT NULL DEFAULT '[]'
                )
            """)
            
            # Create indexes for better performance
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_records_entity_name ON generic_records(entity_name)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_records_source_id ON generic_records(source_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_records_source_record_id ON generic_records(source_record_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_records_owner_id ON generic_records(owner_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_records_created_at ON generic_records(created_at)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_records_data_gin ON generic_records USING GIN(data)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_batches_source_id ON extraction_batches(source_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_batches_status ON extraction_batches(status)")
    
    async def store_data_source(self, source: DataSource) -> None:
        """Store a data source configuration."""
        async with self._pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO data_sources (
                    id, name, source_type, description, connection_config, extraction_config,
                    is_enabled, is_sensitive, excluded_entities, excluded_fields,
                    created_at, updated_at, last_extraction_at, entities
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    source_type = EXCLUDED.source_type,
                    description = EXCLUDED.description,
                    connection_config = EXCLUDED.connection_config,
                    extraction_config = EXCLUDED.extraction_config,
                    is_enabled = EXCLUDED.is_enabled,
                    is_sensitive = EXCLUDED.is_sensitive,
                    excluded_entities = EXCLUDED.excluded_entities,
                    excluded_fields = EXCLUDED.excluded_fields,
                    updated_at = EXCLUDED.updated_at,
                    last_extraction_at = EXCLUDED.last_extraction_at,
                    entities = EXCLUDED.entities
            """, 
                source.id, source.name, source.source_type.value, source.description,
                source.connection_config, source.extraction_config,
                source.is_enabled, source.is_sensitive, 
                list(source.excluded_entities), dict(source.excluded_fields),
                source.created_at, source.updated_at, source.last_extraction_at,
                {name: schema.dict() for name, schema in source.entities.items()}
            )
    
    async def get_data_source(self, source_id: UUID) -> Optional[DataSource]:
        """Get a data source by ID."""
        async with self._pool.acquire() as conn:
            row = await conn.fetchrow("SELECT * FROM data_sources WHERE id = $1", source_id)
            if not row:
                return None
            
            return self._row_to_data_source(row)
    
    async def list_data_sources(self) -> List[DataSource]:
        """List all data sources."""
        async with self._pool.acquire() as conn:
            rows = await conn.fetch("SELECT * FROM data_sources ORDER BY name")
            return [self._row_to_data_source(row) for row in rows]
    
    async def delete_data_source(self, source_id: UUID) -> None:
        """Delete a data source configuration."""
        async with self._pool.acquire() as conn:
            await conn.execute("DELETE FROM data_sources WHERE id = $1", source_id)
    
    def _row_to_data_source(self, row) -> DataSource:
        """Convert database row to DataSource object."""
        from .models import EntitySchema, FieldMetadata, RelationshipMetadata
        
        # Convert entities JSON back to EntitySchema objects
        entities = {}
        for name, schema_data in row['entities'].items():
            # Convert field metadata
            fields = {}
            for field_name, field_data in schema_data.get('fields', {}).items():
                fields[field_name] = FieldMetadata(**field_data)
            
            # Convert relationship metadata
            relationships = []
            for rel_data in schema_data.get('relationships', []):
                relationships.append(RelationshipMetadata(**rel_data))
            
            schema_data['fields'] = fields
            schema_data['relationships'] = relationships
            entities[name] = EntitySchema(**schema_data)
        
        return DataSource(
            id=row['id'],
            name=row['name'],
            source_type=row['source_type'],
            description=row['description'],
            connection_config=row['connection_config'],
            extraction_config=row['extraction_config'],
            is_enabled=row['is_enabled'],
            is_sensitive=row['is_sensitive'],
            excluded_entities=set(row['excluded_entities']),
            excluded_fields={k: set(v) for k, v in row['excluded_fields'].items()},
            created_at=row['created_at'],
            updated_at=row['updated_at'],
            last_extraction_at=row['last_extraction_at'],
            entities=entities
        )
    
    # Additional methods will be implemented in the next part...
