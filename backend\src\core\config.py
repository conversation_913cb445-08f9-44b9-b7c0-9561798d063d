"""
Configuration management for FungAI Backend.

This module handles all configuration settings using Pydantic Settings
for type safety and validation.
"""

from typing import Optional, List
from pydantic import BaseSettings, Field, validator
from enum import Enum


class LogLevel(str, Enum):
    """Available log levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class VectorDBType(str, Enum):
    """Available vector database types."""
    CHROMADB = "chromadb"
    QDRANT = "qdrant"


class Settings(BaseSettings):
    """Application settings."""
    
    # Application Configuration
    app_name: str = Field(default="FungAI Backend", env="APP_NAME")
    app_version: str = Field(default="0.1.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: LogLevel = Field(default=LogLevel.INFO, env="LOG_LEVEL")
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_prefix: str = Field(default="/api/v1", env="API_PREFIX")
    
    # Database Configuration
    database_url: str = Field(env="DATABASE_URL")
    
    # Salesforce Configuration
    salesforce_username: Optional[str] = Field(default=None, env="SALESFORCE_USERNAME")
    salesforce_password: Optional[str] = Field(default=None, env="SALESFORCE_PASSWORD")
    salesforce_security_token: Optional[str] = Field(default=None, env="SALESFORCE_SECURITY_TOKEN")
    salesforce_domain: str = Field(default="login", env="SALESFORCE_DOMAIN")
    salesforce_api_version: str = Field(default="58.0", env="SALESFORCE_API_VERSION")

    # Salesforce OAuth Configuration
    salesforce_consumer_key: Optional[str] = Field(default=None, env="SALESFORCE_CONSUMER_KEY")
    salesforce_consumer_secret: Optional[str] = Field(default=None, env="SALESFORCE_CONSUMER_SECRET")
    salesforce_auth_type: str = Field(default="password", env="SALESFORCE_AUTH_TYPE")  # 'password' or 'oauth'
    
    # Oracle Configuration
    oracle_host: Optional[str] = Field(default=None, env="ORACLE_HOST")
    oracle_port: int = Field(default=1521, env="ORACLE_PORT")
    oracle_service_name: Optional[str] = Field(default=None, env="ORACLE_SERVICE_NAME")
    oracle_username: Optional[str] = Field(default=None, env="ORACLE_USERNAME")
    oracle_password: Optional[str] = Field(default=None, env="ORACLE_PASSWORD")
    
    # SQL Server Configuration
    sqlserver_host: Optional[str] = Field(default=None, env="SQLSERVER_HOST")
    sqlserver_port: int = Field(default=1433, env="SQLSERVER_PORT")
    sqlserver_database: Optional[str] = Field(default=None, env="SQLSERVER_DATABASE")
    sqlserver_username: Optional[str] = Field(default=None, env="SQLSERVER_USERNAME")
    sqlserver_password: Optional[str] = Field(default=None, env="SQLSERVER_PASSWORD")
    sqlserver_driver: str = Field(default="ODBC Driver 17 for SQL Server", env="SQLSERVER_DRIVER")
    
    # Vector Database Configuration
    vector_db_type: VectorDBType = Field(default=VectorDBType.CHROMADB, env="VECTOR_DB_TYPE")
    chromadb_path: str = Field(default="./data/chromadb", env="CHROMADB_PATH")
    qdrant_host: str = Field(default="localhost", env="QDRANT_HOST")
    qdrant_port: int = Field(default=6333, env="QDRANT_PORT")
    qdrant_api_key: Optional[str] = Field(default=None, env="QDRANT_API_KEY")
    
    # LLM Configuration
    llm_model_name: str = Field(default="microsoft/DialoGPT-medium", env="LLM_MODEL_NAME")
    llm_model_path: str = Field(default="./models", env="LLM_MODEL_PATH")
    llm_device: str = Field(default="cpu", env="LLM_DEVICE")
    llm_max_length: int = Field(default=512, env="LLM_MAX_LENGTH")
    embedding_model_name: str = Field(default="sentence-transformers/all-MiniLM-L6-v2", env="EMBEDDING_MODEL_NAME")
    
    # Processing Configuration
    chunk_size: int = Field(default=1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=200, env="CHUNK_OVERLAP")
    max_concurrent_extractions: int = Field(default=5, env="MAX_CONCURRENT_EXTRACTIONS")
    batch_size: int = Field(default=1000, env="BATCH_SIZE")
    
    # Security Configuration
    secret_key: str = Field(env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    
    # Monitoring Configuration
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    
    @validator("chunk_overlap")
    def validate_chunk_overlap(cls, v, values):
        """Ensure chunk overlap is less than chunk size."""
        if "chunk_size" in values and v >= values["chunk_size"]:
            raise ValueError("chunk_overlap must be less than chunk_size")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()
