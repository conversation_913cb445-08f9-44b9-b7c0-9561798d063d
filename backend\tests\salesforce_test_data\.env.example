# Configuration Salesforce pour les tests
# Copiez ce fichier vers backend/.env et remplissez avec vos credentials

# Credentials Salesforce
SF_USERNAME=<EMAIL>
SF_PASSWORD=votre_mot_de_passe
SF_SECURITY_TOKEN=votre_token_de_securite

# Configuration de l'org
SF_DOMAIN=test  # 'login' pour production, 'test' pour sandbox
SF_API_VERSION=58.0

# Configuration des tests
TEST_DATA_COUNT_ACCOUNTS=5
TEST_DATA_COUNT_CONTACTS=15
TEST_DATA_COUNT_PLAYGROUNDS=8
TEST_DATA_COUNT_RESERVATIONS=50

# Logging
LOG_LEVEL=INFO
LOG_FILE=salesforce_tests.log

# Performance
EXTRACTION_BATCH_SIZE=1000
SCHEMA_DISCOVERY_TIMEOUT=120
EXTRACTION_TIMEOUT=300
