"""
Salesforce data extractor implementation.

This module implements the Salesforce-specific extractor that uses
Bulk API 2.0 and Metadata API to extract data into the generic model.
"""

from typing import Dict, List, Optional, AsyncIterator, Any
import asyncio
from datetime import datetime
import json
from loguru import logger

from simple_salesforce import Salesforce
from simple_salesforce.bulk import SFBulkHandler
from simple_salesforce.exceptions import SalesforceError

from ..core.extractors import APIExtractor, ExtractorError, ConnectionError, SchemaDiscoveryError
from ..core.salesforce_auth import SalesforceAuthenticator
from ..core.models import (
    SourceType, EntitySchema, GenericRecord, FieldMetadata, RelationshipMetadata,
    DataType, RelationType
)


class SalesforceExtractor(APIExtractor):
    """
    Salesforce extractor using Bulk API 2.0 and Metadata API.
    
    This extractor discovers Salesforce org schema and extracts data
    using the most efficient APIs available.
    """
    
    def __init__(self, source):
        super().__init__(source)
        self._sf_client = None
        self._bulk_handler = None
        self._authenticator = None
    
    @property
    def source_type(self) -> SourceType:
        """Return Salesforce source type."""
        return SourceType.SALESFORCE
    
    async def connect(self) -> None:
        """Establish connection to Salesforce."""
        try:
            config = self.source.connection_config

            # Créer l'authenticateur
            self._authenticator = SalesforceAuthenticator(config)

            # Authentifier et obtenir la connexion
            self._sf_client = self._authenticator.authenticate()

            self._bulk_handler = SFBulkHandler(
                session_id=self._sf_client.session_id,
                bulk_url=self._sf_client.bulk_url,
                proxies=self._sf_client.proxies,
                session=self._sf_client.session
            )

            self.is_connected = True
            logger.info(f"Connected to Salesforce org: {self._sf_client.sf_instance}")

        except SalesforceError as e:
            raise ConnectionError(f"Failed to connect to Salesforce: {str(e)}")
        except Exception as e:
            raise ConnectionError(f"Unexpected error connecting to Salesforce: {str(e)}")
    
    async def disconnect(self) -> None:
        """Close Salesforce connection."""
        self._sf_client = None
        self._bulk_handler = None
        self._authenticator = None
        self.is_connected = False
        logger.info("Disconnected from Salesforce")
    
    async def test_connection(self) -> bool:
        """Test Salesforce connection."""
        try:
            if not self.is_connected:
                await self.connect()
            
            # Test with a simple query
            result = self._sf_client.query("SELECT Id FROM User LIMIT 1")
            return len(result['records']) >= 0
            
        except Exception as e:
            logger.error(f"Salesforce connection test failed: {str(e)}")
            return False
    
    async def discover_schema(self) -> Dict[str, EntitySchema]:
        """Discover Salesforce org schema using Metadata API."""
        if not self.is_connected:
            await self.connect()
        
        try:
            logger.info("Starting Salesforce schema discovery")
            schemas = {}
            
            # Get all SObject types
            sobject_types = self._sf_client.describe()['sobjects']
            
            for sobject_info in sobject_types:
                sobject_name = sobject_info['name']
                
                # Skip if excluded
                if sobject_name in self.source.excluded_entities:
                    continue
                
                # Skip system objects if not explicitly included
                if self._is_system_object(sobject_name) and not self._should_include_system_object(sobject_name):
                    continue
                
                try:
                    schema = await self._discover_sobject_schema(sobject_name)
                    if schema:
                        schemas[sobject_name] = schema
                        logger.debug(f"Discovered schema for {sobject_name}")
                
                except Exception as e:
                    logger.warning(f"Failed to discover schema for {sobject_name}: {str(e)}")
                    continue
            
            logger.info(f"Discovered {len(schemas)} SObject schemas")
            return schemas
            
        except Exception as e:
            raise SchemaDiscoveryError(f"Failed to discover Salesforce schema: {str(e)}")
    
    async def _discover_sobject_schema(self, sobject_name: str) -> Optional[EntitySchema]:
        """Discover schema for a specific SObject."""
        try:
            # Get detailed SObject description
            sobject_desc = getattr(self._sf_client, sobject_name).describe()
            
            # Convert fields
            fields = {}
            primary_key = []
            relationships = []
            
            for field_info in sobject_desc['fields']:
                field_name = field_info['name']
                
                # Skip if excluded
                if self.source.is_field_excluded(sobject_name, field_name):
                    continue
                
                # Convert field metadata
                field_metadata = self._convert_field_metadata(field_info)
                fields[field_name] = field_metadata
                
                # Track primary key
                if field_info.get('idLookup', False):
                    primary_key.append(field_name)
                
                # Track relationships
                if field_info['type'] == 'reference' and field_info.get('referenceTo'):
                    for ref_object in field_info['referenceTo']:
                        relationship = RelationshipMetadata(
                            name=f"{sobject_name}_{field_name}_to_{ref_object}",
                            relation_type=RelationType.LOOKUP if not field_info.get('cascadeDelete', False) else RelationType.MASTER_DETAIL,
                            source_entity=sobject_name,
                            target_entity=ref_object,
                            source_field=field_name,
                            target_field='Id',
                            is_cascade_delete=field_info.get('cascadeDelete', False),
                            description=f"Lookup from {sobject_name}.{field_name} to {ref_object}.Id",
                            source_metadata={
                                'salesforce_field_info': field_info
                            }
                        )
                        relationships.append(relationship)
            
            # Create entity schema
            schema = EntitySchema(
                name=sobject_name,
                source_type=SourceType.SALESFORCE,
                source_name=sobject_name,
                description=sobject_desc.get('label', sobject_name),
                fields=fields,
                primary_key=primary_key or ['Id'],
                relationships=relationships,
                is_sensitive=self._is_sensitive_object(sobject_name),
                source_metadata={
                    'salesforce_describe': {
                        'label': sobject_desc.get('label'),
                        'labelPlural': sobject_desc.get('labelPlural'),
                        'keyPrefix': sobject_desc.get('keyPrefix'),
                        'custom': sobject_desc.get('custom', False),
                        'createable': sobject_desc.get('createable', False),
                        'updateable': sobject_desc.get('updateable', False),
                        'deletable': sobject_desc.get('deletable', False),
                        'queryable': sobject_desc.get('queryable', False)
                    }
                }
            )
            
            return schema
            
        except Exception as e:
            logger.error(f"Failed to discover schema for {sobject_name}: {str(e)}")
            return None
    
    def _convert_field_metadata(self, field_info: Dict[str, Any]) -> FieldMetadata:
        """Convert Salesforce field info to generic field metadata."""
        # Map Salesforce types to generic types
        type_mapping = {
            'string': DataType.STRING,
            'textarea': DataType.STRING,
            'email': DataType.STRING,
            'phone': DataType.STRING,
            'url': DataType.STRING,
            'picklist': DataType.STRING,
            'multipicklist': DataType.STRING,
            'combobox': DataType.STRING,
            'reference': DataType.REFERENCE,
            'id': DataType.STRING,
            'boolean': DataType.BOOLEAN,
            'int': DataType.INTEGER,
            'double': DataType.FLOAT,
            'currency': DataType.FLOAT,
            'percent': DataType.FLOAT,
            'date': DataType.DATE,
            'datetime': DataType.DATETIME,
            'time': DataType.STRING,
            'base64': DataType.BINARY,
            'anyType': DataType.JSON
        }
        
        sf_type = field_info['type']
        generic_type = type_mapping.get(sf_type, DataType.STRING)
        
        return FieldMetadata(
            name=field_info['name'],
            data_type=generic_type,
            is_required=not field_info.get('nillable', True),
            is_unique=field_info.get('unique', False),
            is_indexed=field_info.get('idLookup', False) or field_info.get('externalId', False),
            is_sensitive=self._is_sensitive_field(field_info),
            max_length=field_info.get('length'),
            default_value=field_info.get('defaultValue'),
            description=field_info.get('label', field_info['name']),
            source_name=field_info['name'],
            source_type=sf_type,
            source_metadata={
                'salesforce_field_info': field_info
            }
        )
    
    def _is_system_object(self, sobject_name: str) -> bool:
        """Check if SObject is a system object."""
        system_prefixes = ['__', 'SBQQ__', 'FSL__']  # Add more as needed
        return any(sobject_name.startswith(prefix) for prefix in system_prefixes)
    
    def _should_include_system_object(self, sobject_name: str) -> bool:
        """Check if system object should be included."""
        # Include commonly used system objects
        important_system_objects = {
            'User', 'Profile', 'PermissionSet', 'Organization',
            'RecordType', 'BusinessHours', 'Holiday'
        }
        return sobject_name in important_system_objects
    
    def _is_sensitive_object(self, sobject_name: str) -> bool:
        """Check if SObject contains sensitive data."""
        sensitive_objects = {
            'User', 'Contact', 'Lead', 'PersonAccount',
            'LoginHistory', 'AuthSession'
        }
        return sobject_name in sensitive_objects
    
    def _is_sensitive_field(self, field_info: Dict[str, Any]) -> bool:
        """Check if field contains sensitive data."""
        field_name = field_info['name'].lower()
        sensitive_patterns = [
            'ssn', 'social', 'tax', 'credit', 'password', 'secret',
            'salary', 'income', 'birthdate', 'dob', 'phone', 'email'
        ]
        return any(pattern in field_name for pattern in sensitive_patterns)
    
    async def get_entity_schema(self, entity_name: str) -> Optional[EntitySchema]:
        """Get schema for a specific SObject."""
        schemas = await self.discover_schema()
        return schemas.get(entity_name)
    
    async def extract_entity_data(
        self, 
        entity_name: str, 
        batch_size: int = 1000,
        filters: Optional[Dict[str, Any]] = None
    ) -> AsyncIterator[List[GenericRecord]]:
        """Extract data from a Salesforce SObject using Bulk API 2.0."""
        if not self.is_connected:
            await self.connect()
        
        try:
            # Get entity schema
            schema = await self.get_entity_schema(entity_name)
            if not schema:
                raise ExtractorError(f"Schema not found for entity: {entity_name}")
            
            # Build SOQL query
            fields = list(schema.fields.keys())
            soql = f"SELECT {', '.join(fields)} FROM {entity_name}"
            
            if filters:
                where_conditions = []
                for field, value in filters.items():
                    if isinstance(value, str):
                        where_conditions.append(f"{field} = '{value}'")
                    else:
                        where_conditions.append(f"{field} = {value}")
                
                if where_conditions:
                    soql += f" WHERE {' AND '.join(where_conditions)}"
            
            logger.info(f"Extracting {entity_name} with query: {soql}")
            
            # Use Bulk API for large datasets
            job_id = self._bulk_handler.create_query_job(entity_name, contentType='CSV')
            batch_id = self._bulk_handler.query(job_id, soql)
            
            # Wait for completion
            while True:
                batch_info = self._bulk_handler.batch_status(job_id, batch_id)
                if batch_info['state'] in ['Completed', 'Failed']:
                    break
                await asyncio.sleep(1)
            
            if batch_info['state'] == 'Failed':
                raise ExtractorError(f"Bulk query failed: {batch_info.get('stateMessage', 'Unknown error')}")
            
            # Get results
            result_data = self._bulk_handler.get_batch_results(job_id, batch_id)
            
            # Convert to GenericRecord objects
            records = []
            for row in result_data:
                record = GenericRecord(
                    entity_name=entity_name,
                    source_type=SourceType.SALESFORCE,
                    source_id=row.get('Id', ''),
                    data=row,
                    source_created_at=self._parse_sf_datetime(row.get('CreatedDate')),
                    source_updated_at=self._parse_sf_datetime(row.get('LastModifiedDate')),
                    owner_id=row.get('OwnerId'),
                    created_by_id=row.get('CreatedById'),
                    last_modified_by_id=row.get('LastModifiedById'),
                    source_metadata={
                        'salesforce_record_type': row.get('RecordTypeId'),
                        'salesforce_system_modstamp': row.get('SystemModstamp')
                    }
                )
                records.append(record)
                
                # Yield in batches
                if len(records) >= batch_size:
                    yield records
                    records = []
            
            # Yield remaining records
            if records:
                yield records
                
        except Exception as e:
            raise ExtractorError(f"Failed to extract data from {entity_name}: {str(e)}")
    
    async def get_record_count(
        self, 
        entity_name: str,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """Get record count for a Salesforce SObject."""
        if not self.is_connected:
            await self.connect()
        
        try:
            soql = f"SELECT COUNT() FROM {entity_name}"
            
            if filters:
                where_conditions = []
                for field, value in filters.items():
                    if isinstance(value, str):
                        where_conditions.append(f"{field} = '{value}'")
                    else:
                        where_conditions.append(f"{field} = {value}")
                
                if where_conditions:
                    soql += f" WHERE {' AND '.join(where_conditions)}"
            
            result = self._sf_client.query(soql)
            return result['totalSize']
            
        except Exception as e:
            logger.error(f"Failed to get record count for {entity_name}: {str(e)}")
            return 0
    
    def _parse_sf_datetime(self, dt_string: Optional[str]) -> Optional[datetime]:
        """Parse Salesforce datetime string."""
        if not dt_string:
            return None
        
        try:
            # Salesforce datetime format: 2023-01-01T12:00:00.000+0000
            return datetime.fromisoformat(dt_string.replace('Z', '+00:00'))
        except Exception:
            return None
    
    def get_required_connection_params(self) -> List[str]:
        """Get required connection parameters for Salesforce."""
        return ['username', 'password']
    
    # Implement abstract methods from APIExtractor
    async def make_request(self, endpoint: str, method: str = "GET", **kwargs) -> Dict[str, Any]:
        """Make API request to Salesforce."""
        # This would implement direct REST API calls if needed
        raise NotImplementedError("Direct API requests not implemented for Salesforce extractor")
    
    async def get_api_schema(self) -> Dict[str, Any]:
        """Get Salesforce API schema."""
        return await self.discover_schema()
