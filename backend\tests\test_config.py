"""
Tests for configuration management.
"""

import pytest
from unittest.mock import patch
from src.core.config import Settings, LogLevel, VectorDBType


def test_default_settings():
    """Test that default settings are loaded correctly."""
    with patch.dict('os.environ', {}, clear=True):
        settings = Settings()
        
        assert settings.app_name == "FungAI Backend"
        assert settings.app_version == "0.1.0"
        assert settings.debug is False
        assert settings.log_level == LogLevel.INFO
        assert settings.api_host == "0.0.0.0"
        assert settings.api_port == 8000


def test_environment_override():
    """Test that environment variables override defaults."""
    env_vars = {
        'APP_NAME': 'Test App',
        'DEBUG': 'true',
        'API_PORT': '9000',
        'LOG_LEVEL': 'DEBUG'
    }
    
    with patch.dict('os.environ', env_vars, clear=True):
        settings = Settings()
        
        assert settings.app_name == "Test App"
        assert settings.debug is True
        assert settings.api_port == 9000
        assert settings.log_level == LogLevel.DEBUG


def test_vector_db_type_validation():
    """Test vector database type validation."""
    with patch.dict('os.environ', {'VECTOR_DB_TYPE': 'chromadb'}, clear=True):
        settings = Settings()
        assert settings.vector_db_type == VectorDBType.CHROMADB
    
    with patch.dict('os.environ', {'VECTOR_DB_TYPE': 'qdrant'}, clear=True):
        settings = Settings()
        assert settings.vector_db_type == VectorDBType.QDRANT


def test_chunk_overlap_validation():
    """Test that chunk overlap validation works."""
    env_vars = {
        'CHUNK_SIZE': '1000',
        'CHUNK_OVERLAP': '1200'  # Invalid: overlap > size
    }
    
    with patch.dict('os.environ', env_vars, clear=True):
        with pytest.raises(ValueError, match="chunk_overlap must be less than chunk_size"):
            Settings()


def test_required_fields():
    """Test that required fields raise validation errors when missing."""
    with patch.dict('os.environ', {}, clear=True):
        with pytest.raises(ValueError):
            # DATABASE_URL and SECRET_KEY are required
            Settings()
