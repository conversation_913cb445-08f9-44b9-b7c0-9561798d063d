version: '3.8'

services:
  # PostgreSQL database for application data
  postgres:
    image: postgres:15-alpine
    container_name: fungai-postgres
    environment:
      POSTGRES_DB: fungai
      POSTGRES_USER: fungai
      POSTGRES_PASSWORD: fungai_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U fungai"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Qdrant vector database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: fungai-qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:6333/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: fungai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FungAI Backend Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fungai-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*************************************************/fungai
      - VECTOR_DB_TYPE=qdrant
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - DEBUG=true
      - LOG_LEVEL=INFO
    volumes:
      - ./backend:/app
      - backend_data:/app/data
      - backend_models:/app/models
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      qdrant:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: fungai-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./backend/docker/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    depends_on:
      - backend
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: fungai-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./backend/docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./backend/docker/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    profiles:
      - monitoring

volumes:
  postgres_data:
  qdrant_data:
  redis_data:
  backend_data:
  backend_models:
  backend_logs:
  prometheus_data:
  grafana_data:

networks:
  default:
    name: fungai-network
